{"scripts": {"prepare": "husky install", "prettier-check": "prettier --check \"{,**/}*.{md,json,yml,html,js,ts,tsx,css,scss,vue,java}\"", "prettier-format": "prettier --write \"{,**/}*.{md,json,yml,html,js,ts,tsx,css,scss,vue,java}\"", "predebug": "npx ts-node src/helper/report/init.ts", "debug": "cross-env ENV=staging PWDEBUG=1 DEBUG=pw:api cucumber-js --config=config/cucumber.js", "debug:fast": "cross-env ENV=staging PWDEBUG=1 DEBUG=pw:api CUCUMBER_TIMEOUT=30 cucumber-js --config=config/cucumber.js", "debug:slow": "cross-env ENV=staging PWDEBUG=1 DEBUG=pw:api CUCUMBER_TIMEOUT=120 cucumber-js --config=config/cucumber.js", "pretest": "npx ts-node src/helper/report/init.ts", "test": "cross-env ENV=staging FORCE_COLOR=0 cucumber-js --config=config/cucumber.js", "posttest": "npx ts-node src/helper/report/report.ts", "test:failed": "cross-env ENV=staging FORCE_COLOR=0 cucumber-js --config config/cucumber.js --profile rerun @rerun.txt"}, "dependencies": {"@axe-core/playwright": "^4.10.2", "@cucumber/cucumber": "^11.3.0", "axios": "^1.6.7", "husky": "^9.1.7", "lodash": "^4.17.21", "playwright": "^1.54.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.40.1", "@types/node": "^24.0.10", "cross-env": "^7.0.3", "dotenv": "^17.2.0", "fs-extra": "^11.3.0", "lint-staged": "11.0.0", "multiple-cucumber-html-reporter": "^3.9.3", "ts-node": "^10.9.2"}, "engines": {"node": "^18.13.0 || >= 20.6.1"}}