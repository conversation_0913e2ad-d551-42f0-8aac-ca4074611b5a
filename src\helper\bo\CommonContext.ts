import { Page } from "@playwright/test";
import { CreatedUser, Facility, FaciltyGroup } from "../types/user";
import { CreatedRoles } from "../types/role";

export default class CommonContext {
    constructor() {
        this.users = [];
        this.roles = [];
        this.filteredApps = [];
        this.facility = [];
        this.faciltygroup = [];
    }
    public users?: CreatedUser[];
    public roles?: CreatedRoles[];
    public filteredApps?: string[];
    public facility?: Facility[];
    public faciltygroup?: FaciltyGroup[];
    //get latest user
    getLatestUser(): CreatedUser | undefined {
        if (this.users.length === 0) return undefined;
        return this.users[this.users.length - 1];
    }
    //get latest Role
    getLatestRole(): CreatedRoles | undefined {
        if (this.roles.length === 0) return undefined;
        return this.roles[this.roles.length - 1];
    }
    //get latest Facility
    getLatestFacility(): Facility | undefined {
        if (this.facility.length === 0) return undefined;
        return this.facility[this.facility.length - 1];
    }
    //get latest Facility Group
    getLatestFacilityGroup(): FaciltyGroup | undefined {
        if (this.faciltygroup.length === 0) return undefined;
        return this.faciltygroup[this.faciltygroup.length - 1];
    }


}



