const report = require("multiple-cucumber-html-reporter");
var os = require("os");
var hostname = os.hostname();
var ostype = os.type();
var osVersion = os.version();
report.generate({
    jsonDir: "test-results",
    reportPath: "test-results/reports/",
    reportName: "ECPS Automation Report",
    pageTitle: "ECPS test report",
    displayDuration: false,
    metadata: {
        browser: {
            name: "chrome",
            version: "latest",
        },
        device: hostname,
        platform: {
            name: ostype,
            version: osVersion,
        },
    },
    customData: {
        title: "Test Info",
        data: [
            { label: "Project", value: "ECPS Application" },
            { label: "Release", value: "1" }
        ],
    },
});