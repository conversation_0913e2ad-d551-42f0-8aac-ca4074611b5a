export { };

declare global {
    namespace NodeJS {
        interface ProcessEnv {
            BROWSER: "chrome" | "firefox" | "webkit",
            ENV: "staging" | "prod" | "test",
            TESTURL: string,
            BASEURL: string,
            SUPPLIER_URL: string,
            HEAD: "true" | "false",
            ENABLE_VIDEOS: "true" | "false",
            ENABLE_TRACING: "true" | "false",
            CUCUMBER_TIMEOUT: string
        }
    }
}