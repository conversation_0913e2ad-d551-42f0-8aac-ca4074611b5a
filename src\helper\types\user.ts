export interface User {
    firstName: string,
    lastName: string,
    username: string,
    email: string,
    phoneno: string,
    position: string,
    password: string,
    address: Address
    role?: string,
    phoneType?: string,
    addressType?: string,
    thirdparty?: string,
    company?: string,
    functionalArea?: string
}

export interface ExtUser {
    role?: string,
    thirdparty?: string,
}

export interface FaciltyGroup {
    groupname: string,
}

export interface Facility {
    facilityname: string,
    divisionnumber: string | number,
    clientnumber: string | number
    address: Address
}

export interface CreatedUser {
    username: string,
    password: string,
    firstName?: string,
    lastName?: string
}


export interface Address {
    addressLine1: string,
    addressLine2: string,
    city: string,
    postcode: string,
    state?: string
}

export interface RegisterTableData {
    company: string,
    role: string,
    phoneType: string,
    addressType: string,
    thirdparty: string
}
