import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { expect } from '@playwright/test';

export class ExcelHelper {
  private filePath: string;
  private workbook: XLSX.WorkBook | null = null;

  constructor(filePath: string) {
    this.filePath = path.resolve(filePath);
    if (!fs.existsSync(this.filePath)) {
      throw new Error(`Excel file does not exist: ${this.filePath}`);
    }
    this.workbook = XLSX.readFile(this.filePath);
  }

  /** Get all sheet names */
  getSheetNames(): string[] {
    return this.workbook?.SheetNames || [];
  }

  /** Get worksheet by name */
  private getWorksheet(sheetName: string): XLSX.WorkSheet {
    if (!this.workbook) throw new Error('Workbook not loaded');
    const sheet = this.workbook.Sheets[sheetName];
    if (!sheet) throw new Error(`Sheet not found: ${sheetName}`);
    return sheet;
  }

  /** Convert sheet to JSON */
  getSheetData(sheetName: string): any[] {
    const ws = this.getWorksheet(sheetName);
    return XLSX.utils.sheet_to_json(ws, { defval: '' }); // defval: '' keeps empty cells
  }

  /** Get value of a specific cell */
  getCellValue(sheetName: string, cellAddress: string): any {
    const ws = this.getWorksheet(sheetName);
    return ws[cellAddress]?.v ?? null;
  }

  /** Write data back to file */
  writeSheet(sheetName: string, data: any[]): void {
    const ws = XLSX.utils.json_to_sheet(data);
    if (!this.workbook) throw new Error('Workbook not loaded');
    this.workbook.Sheets[sheetName] = ws;
    XLSX.writeFile(this.workbook, this.filePath);
  }


  static async downloadAndVerifyExcel(
    page: any,
    exportButton: any,
    options: {
      roleName?: string;                  // optional, used in regex
      regexPattern?: RegExp;              // file name regex
      expectedHeaders: string[];          // headers to validate
      headerRowIndex?: number;            // default = 3 (4th row in Excel)
      downloadFileName?: string;          // default = "report.xlsx"
    }
  ): Promise<void> {
    const {
      roleName,
      regexPattern,
      expectedHeaders,
      headerRowIndex = 3,
      downloadFileName = 'report.xlsx',
    } = options;

    const [download] = await Promise.all([
      page.waitForEvent('download'),
      exportButton.click(), // ⬅️ assumes locator is passed
    ]);

    // ✅ Verify file name with regex
    const suggestedFileName = download.suggestedFilename();
    const finalRegex =
      regexPattern ||
      new RegExp(`^${roleName ?? 'User'}_Export_\\d{4}-\\d{2}-\\d{2}\\.xlsx$`);
    expect(suggestedFileName).toMatch(finalRegex);
    console.log('Excel File Name:', suggestedFileName);

    // Save file
    const downloadPath = path.resolve(__dirname, `../downloads/${downloadFileName}`);
    fs.mkdirSync(path.dirname(downloadPath), { recursive: true });
    await download.saveAs(downloadPath);

    // ✅ Validate Excel file
    const excel = new ExcelHelper(downloadPath);
    const sheetName = excel.getSheetNames()[0];
    const data = excel.getSheetData(sheetName);

    const headerRow = data[headerRowIndex];
    if (!headerRow) throw new Error('Header row not found in Excel');

    const actualHeaders = Object.values(headerRow);
    console.log('Actual headers:', actualHeaders);

    // Verify all expected headers are present
    for (const header of expectedHeaders) {
      expect(actualHeaders).toContain(header);
    }

    // Optional: exact order match
    expect(actualHeaders).toEqual(expectedHeaders);

    console.log('Excel sheetName:', sheetName);
    expect(data.length).toBeGreaterThan(0);

    // 🧹 Cleanup
    if (fs.existsSync(downloadPath)) {
      fs.unlinkSync(downloadPath);
      console.log(`Deleted downloaded file: ${downloadPath}`);
    }
  }


}
