import { User ,FaciltyGroup,Facility} from "../types/user";
import { Roles } from "../types/role";

const { faker } = require('@faker-js/faker');

export function generateUser(): User {
  return {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email().toLowerCase(),
    username: faker.internet.username(),
    phoneno: faker.phone.number({ style: 'human' }).replace(/\D/g, ""),
    position: faker.person.jobTitle(),
    password: "P@ss*or%@123",
    address: {
        addressLine1: faker.location.streetAddress(),
        addressLine2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        postcode: faker.location.zipCode()
    }
  };
}




export function generateRole(): Roles {
    return {
        rolename: faker.person.jobTitle(),
        roledescription: faker.person.jobDescriptor()
    };
} 

export function generatefacilitygroup(): FaciltyGroup{
  return {
    groupname: faker.company.name(),
  };
}

export function generatefacility(): Facility{
  return {
    facilityname: faker.company.name(),
    divisionnumber: faker.string.numeric(5),
    clientnumber: faker.string.numeric(5),
    address: {
        addressLine1: faker.location.streetAddress(),
        addressLine2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        postcode: faker.location.zipCode()
    }

  };
}