import { Locator, <PERSON> } from "@playwright/test";
import path = require("path");

export const clickElement = async (element: Locator) => {
  await element.click();
};

export const waitAndClickElement = async (element: Locator, delayTime: any) => {
  await element.click({ delay: delayTime });
};

export const forceClickElement = async (element: Locator) => {
  await element.click({ force: true });
};

export const doubleClickElement = async (element: Locator) => {
  await element.dblclick();
};

export const rightClickElement = async (element: Locator) => {
  await element.click({ button: "right" });
};

export const fillElement = async (element: Locator, input: any) => {
  await element.fill(input);
};

export const pressEnterOnElement = async (element: Locator) => {
  await element.press("Enter");
};

export const checkElement = async (element: Locator) => {
  await element.check();
};

export const uncheckElement = async (element: Locator) => {
  await element.uncheck();
};

export const selectElementByValue = async (
  element: Locator,
  optionValue: string
) => {
  await element.selectOption(optionValue);
};

export const selectElementByLabelName = async (
  element: Locator,
  labelName: string
) => {
  await element.selectOption({ label: labelName });
};

export const hoveOverElement = async (element: Locator) => {
  await element.hover();
};

export const focusElement = async (element: Locator) => {
  await element.focus();
};

export const uploadFile = async (element: Locator, fileName: string) => {
  await element.setInputFiles(path.join(__dirname, fileName));
};

export const dragNDrop = async (
  sourceElement: Locator,
  destElement: Locator
) => {
  await sourceElement.dragTo(destElement);
};

export const scrollIntoView = async (element: Locator) => {
  await element.scrollIntoViewIfNeeded();
};

export const takeScreenshot = async (element: Locator) => {
  await element.screenshot();
};
