import { expect, type Page } from '@playwright/test';
import { AxeBuilder } from '@axe-core/playwright';
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import {clickElement,forceClickElement} from "../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";

export default class AccessibilityPage {
    

    private base: PlaywrightWrapper;
    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        
    }

    private Elements = {
        searchUser: this.page.getByLabel('Search'),
        filterByUser: this.page.getByRole('button', { name: 'Filter By' }),
        firstRow: this.page.locator("button[aria-label='View User']").nth(1),
        editUser: this.page.getByLabel('Edit'),
        users: this.page.getByRole('link', { name: 'Users' }),
        addNewUser: this.page.getByLabel('Add New User'),
        selectBrand: this.page.locator('div').filter({ hasText: /^Brand\*Select one$/ }).getByLabel('Select one'),
        clickBrand: this.page.getByRole('menuitem', { name: 'CPS' }),
        addNewPhone: this.page.locator('div').filter({ hasText: /^Phone Numbers\+ ADD NEW$/ }).getByRole('button'),
        addNwadd: this.page.locator('div').filter({ hasText: /^Addresses\+ ADD NEW$/ }).getByRole('button'),
        addType: this.page.getByRole('button', { name: 'Add New', exact: true }),
        funcArea:this.page.getByRole('combobox', { name: 'Functional Area' }),
        roles:this.page.getByRole('link', { name: 'Roles' }),
        discardChanges: this.page.getByLabel('Yes, Discard changes'),
        firstRowRole: this.page.locator("button[aria-label='View Role']").nth(1),
        createRole: this.page.getByLabel('Create New Role'),
        assignUs: this.page.getByRole('button', { name: 'Plus Assign' }),
        searchUs: this.page.getByRole('button', { name: 'Search' }),
        closeSearchP: this.page.getByRole('button', { name: 'Close Cross' }),
        facilities: this.page.getByRole('link', { name: 'Facilities' }),
        addNewFacG: this.page.getByLabel('Add New Facility Group'),
        back: this.page.getByLabel('Go back'),
        addNewFac: this.page.getByLabel('Add New Facility', { exact: true }),
        facedit: this.page.locator('#facilities'),
        firstRowFG: this.page.locator("button[aria-label='View Facility Group']").nth(1),
        firstRowF: this.page.locator("button[aria-label='View Facility']").nth(1),
        editF: this.page.getByLabel('Edit', { exact: true })
        
    }
    async createLogAndAssert(page: Page, str: string ) {
        const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
        await fixture.logger.info(str+" violations");
        await fixture.logger.info(JSON.stringify(accessibilityScanResults.violations));
        //expect(accessibilityScanResults.violations).toEqual([]);
    }
    async userAccesablity(page: Page) {
        await clickElement(this.Elements.searchUser);
        await clickElement(this.Elements.filterByUser);
        await this.createLogAndAssert(page,"search user");
        await clickElement(this.Elements.firstRow);
        await this.createLogAndAssert(page,"view user");
        await clickElement(this.Elements.editUser);
        await this.createLogAndAssert(page,"edit user");
        await clickElement(this.Elements.users);
        await clickElement(this.Elements.addNewUser);
        await clickElement(this.Elements.selectBrand);
        await clickElement(this.Elements.clickBrand);
        await this.base.escapeOnDropdown();
        await clickElement(this.Elements.addNewPhone);
        await clickElement(this.Elements.addNwadd);
        await clickElement(this.Elements.addType);
        await forceClickElement(this.Elements.funcArea);
        await this.createLogAndAssert(page,"create user");
    }
    async roleAccesablity(page: Page) {
        await clickElement(this.Elements.roles);
        await clickElement(this.Elements.discardChanges);
        await this.createLogAndAssert(page,"list roles");
        await clickElement(this.Elements.firstRowRole);
        await this.createLogAndAssert(page,"view roles");
        await clickElement(this.Elements.editUser);
        await this.createLogAndAssert(page,"edit roles");
        await clickElement(this.Elements.roles);
        await clickElement(this.Elements.createRole);
        await this.createLogAndAssert(page,"create roles");
        await clickElement(this.Elements.assignUs);
        await clickElement(this.Elements.searchUs);
        await this.createLogAndAssert(page,"assign user roles");
        await clickElement(this.Elements.closeSearchP);
    }
    async facilitiesAccesablity(page: Page) {
        await clickElement(this.Elements.facilities);
        await clickElement(this.Elements.searchUser);
        await clickElement(this.Elements.filterByUser);
        await this.createLogAndAssert(page,"Search FGs");
        await clickElement(this.Elements.firstRowFG);
        await this.createLogAndAssert(page,"view FGs");
        await clickElement(this.Elements.editF);
        await this.createLogAndAssert(page,"edit FGs");
        await clickElement(this.Elements.facilities);
        await clickElement(this.Elements.addNewFacG);
        await this.createLogAndAssert(page,"Add FGs");
        await clickElement(this.Elements.back);
        await clickElement(this.Elements.addNewFac);
        await this.createLogAndAssert(page,"Add FG");
        await clickElement(this.Elements.back);
        await clickElement(this.Elements.facedit);
        await clickElement(this.Elements.searchUser);
        await clickElement(this.Elements.firstRowF);
        await this.createLogAndAssert(page,"view FG");
        await clickElement(this.Elements.editUser);
        await this.createLogAndAssert(page,"edit FG");
    }
    async runAccessibilityReport() {
        await this.base.goto(process.env.TESTURL);
        await setTimeout(5000);
        if(this.page.url()!=process.env.TESTURL){
            await this.base.commonLogin();
        }
        //await this.base.waitForResponse('api/MasterData/facilities/filter');
        const page: Page = fixture.page;
        await this.userAccesablity(page);
        await this.roleAccesablity(page);
        await this.facilitiesAccesablity(page);
    }
}