import { expect, Page } from "@playwright/test";
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { User } from "../helper/types/user";
import { generateUser } from "../helper/util/common";
import { clickElement, fillElement, forceClickElement, waitAndClickElement } from "../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";

/**
 * Page Object Model for Edit User Page.
 * Provides methods to interact with and verify UI elements, edit user details, roles, contact info, third party info, and perform user update actions.
 */
export default class EditUserPage {

    private base: PlaywrightWrapper;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
    }

    // Locators for Edit User Page elements
    private elements = {
        internalTab: this.page.locator('a').filter({ hasText: /^Internal\*$/ }),
        newInternalTab: this.page.locator('a').filter({ hasText: /^Manage User - New\*$/ }),
        addNewUserLink: this.page.locator('a[href="/add-user"]'),
        firstNameInput: this.page.getByRole('textbox', { name: 'First Name' }),
        lastNameInput: this.page.getByRole('textbox', { name: 'Last Name' }),
        editCompanyButton: this.page.locator('text=QUASEP').or(this.page.locator('text=GESPRA')).or(this.page.getByRole('button', { name: 'CPS', exact: true })),
        languageDropdown: this.page.locator('div').filter({ hasText: /^Language\*Select one$/ }).getByRole('button').nth(1),
        languageSelectOption: this.page.getByRole('option', { name: 'English' }),
        statusSwitch: this.page.getByRole('switch'),
        positionInput: this.page.getByRole('textbox', { name: 'Position' }),
        facilityGroupDropdown: this.page.getByTitle('combobox'),
        facilityDropdown: this.page.locator('div').filter({ hasText: /^Principal Facility\*Select one$/ }),
        functionalAreaDropdown: this.page.getByRole('combobox', { name: 'Functional Area' }),
        functionalAreaSelectOption: this.page.getByText('Administration'),
        usernameInput: this.page.getByRole('textbox', { name: 'Username' }),
        editUsernameInput: this.page.locator('input[data-slot="input"][placeholder="Enter username"]'),
        passwordInput: this.page.getByRole('textbox', { name: 'Password' }),
        roleSelectionButton: this.page.locator('div.px-4.py-1 button[aria-haspopup="menu"]:nth-child(2)'),
        emailInput: this.page.getByRole('textbox', { name: 'Email' }),
        addNewPhoneButton: this.page.locator('div').filter({ hasText: /^Phone Numbers\+ ADD NEW$/ }).getByLabel('+ ADD NEW'),
        phoneNumberInput: this.page.getByRole('textbox', { name: 'Phone Number' }),
        phoneTypeDropdown: this.page.locator('button[name="contactDetails.phoneNumbers.0.phoneType"]'),
        addNewAddressButton: this.page.locator('div').filter({ hasText: /^Addresses\+ ADD NEW$/ }).getByLabel('+ ADD NEW'),
        addressLine1Input: this.page.getByRole('textbox', { name: 'Address Line 1' }),
        addressLine2Input: this.page.getByRole('textbox', { name: 'Address Line 2' }),
        cityInput: this.page.getByRole('textbox', { name: 'City' }),
        postalCodeInput: this.page.getByRole('textbox', { name: 'Postal Code' }),
        stateProvinceButton: this.page.getByRole('button', { name: 'Select Province' }),
        addressTypeButton: this.page.locator('button[aria-label="Main"]'),
        addThirdPartyButton: this.page.getByRole('button', { name: 'Add New', exact: true }),
        thirdPartyDropdown: this.page.getByRole('button', { name: 'Select one' }),
        editThirdPartyDropdown: this.page.getByRole('cell', { name: 'Select one' }).getByLabel('Select one'),
        thirdPartyUsernameInput: this.page.getByRole('textbox', { name: 'Username', exact: true }),
        editThirdPartyUsernameInput: this.page.locator('input[name="authorisations.0.username"]'),
        submitButton: this.page.getByRole('button', { name: 'Submit' }),
        saveButton: this.page.getByRole('button', { name: 'Save' }),
        accountManagerButton: this.page.locator('button[name="roleSelection\\.regionalDirector"]'),
        accountManager1Button: this.page.locator('button[name="roleSelection.accountManager"]'),
        primaryProvinceButton: this.page.locator('button[name="roleSelection\\.primaryProvince"]'),
        secondaryProvinceButton: this.page.getByLabel('Additional Provinces'),
        instructorPortalButton: this.page.locator('button[aria-label="Instructor Portal Department"]'),
        instructorPortalTypeOption: this.page.getByLabel('Suggestions').getByText('Administration'),
        cfmaCheckbox: this.page.getByLabel('CMFA'),
        confirmInactiveButton: this.page.getByRole('button', { name: 'Yes, Continue' }),
        userHomeLink: this.page.getByRole('link', { name: 'Users' }),
        userEditButton: this.page.getByRole('button', { name: 'Edit' }),
        userGridEditButton: this.page.locator('tbody tr:nth-child(2) td:nth-child(8) button[aria-label="Edit User"]'),
        userGridViewButton: this.page.locator('tbody tr:nth-child(2) td:nth-child(8) button[aria-label="View User"]')
    };

    private companyOption = (name: string) => this.page.getByRole('menuitem', { name });
    private verifyText = (text: string) => this.page.getByText(text, { exact: true }).first();
    private phoneTypeOption = (type: string) => this.page.getByRole('menuitem', { name: type, exact: true });
    private roleOption = (role: string) => this.page.getByRole('menuitem', { name: role, exact: true });
    private thirdPartyOption = (name: string) => this.page.getByRole('menuitem', { name, exact: true });
    private regManagerOption = this.page.locator('div[data-slot="dropdown-menu-content"] div[data-slot="dropdown-menu-item"]').nth(0);
    private fgOptionSelector: string = 'div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]';
    private facilityGroupOption = (i: number) => this.page.locator(this.fgOptionSelector).nth(i);
    private newRoleSelect = this.page.locator('.grid > div > .peer').first();
    private facOptionSelector: string = 'div[data-slot="command-group"] div[data-slot="command-item"]';
    private facilityOption = (name: string) => this.page.locator(`${this.facOptionSelector}:has-text("${name}")`);

    /**
     * Navigates to the User Home page.
     */
    async navigateToUserHome(): Promise<void> {
        await clickElement(this.elements.userHomeLink);
    }

    /**
     * Edits user details including name, company, position, facility group, facility, functional area, username, and password.
     * @param user - User object containing user details.
     * @param company - Company name to select.
     * @param functionalArea - Functional area(s) to select (comma separated).
     */
    async editUserDetails(user: User, company: string, functionalArea: string): Promise<void> {
        await clickElement(this.elements.firstNameInput);
        await fillElement(this.elements.firstNameInput, user.firstName);
        await clickElement(this.elements.lastNameInput);
        await fillElement(this.elements.lastNameInput, user.lastName);
        await clickElement(this.elements.editCompanyButton);
        await clickElement(this.companyOption(company));
        await clickElement(this.elements.positionInput);
        await fillElement(this.elements.positionInput, user.position);
        await waitAndClickElement(this.elements.facilityGroupDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupOption(0));
        await waitAndClickElement(this.elements.facilityDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupOption(0));
        await waitAndClickElement(this.elements.functionalAreaDropdown, 300);
        for (const area of functionalArea.split(", ")) {
            await fixture.page.waitForSelector(this.facOptionSelector);
            await forceClickElement(this.facilityOption(area));
        }
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.editUsernameInput);
        await fillElement(this.elements.editUsernameInput, user.username);
        await clickElement(this.elements.passwordInput);
        await fillElement(this.elements.passwordInput, user.password);
    }

    /**
     * Edits user roles and related selections.
     * @param role - Role name to select.
     */
    async editUserRoles(role: string): Promise<void> {
        await clickElement(this.elements.roleSelectionButton);
        await clickElement(this.roleOption(role));
        if (role === 'Account Manager' || role === 'Regional Director' || role === 'Client Group Admin') {
            await setTimeout(1500);
            if (role !== 'Regional Director') {
                await clickElement(role === "Client Group Admin" ? this.elements.accountManager1Button : this.elements.accountManagerButton);
                await forceClickElement(this.regManagerOption);
            }
            await clickElement(this.elements.primaryProvinceButton);
            await setTimeout(500);
            await forceClickElement(this.regManagerOption);
        } else if (role === 'Education Coordinator') {
            await setTimeout(1000);
            await clickElement(this.elements.instructorPortalButton);
            await clickElement(this.elements.instructorPortalTypeOption);
            await clickElement(this.elements.cfmaCheckbox);
        }
        await clickElement(this.newRoleSelect);
    }

    /**
     * Edits user contact details including email, phone, and address.
     * @param user - User object containing contact details.
     * @param phoneType - Phone type to select.
     * @param addressType - Address type to select.
     */
    async editContactDetails(user: User, phoneType: string, addressType: string): Promise<void> {
        await clickElement(this.elements.emailInput);
        await fillElement(this.elements.emailInput, user.email);
        await clickElement(this.elements.addNewPhoneButton);
        await clickElement(this.elements.phoneNumberInput);

        if (phoneType === 'Cell/Mobile' && user.phoneno.length > 10) {
            user.phoneno = user.phoneno.substring(0, 10);
        }
        await fillElement(this.elements.phoneNumberInput, user.phoneno);
        if (phoneType !== "Main") {
            await clickElement(this.elements.phoneTypeDropdown);
            await clickElement(this.phoneTypeOption(phoneType));
        }
        await clickElement(this.elements.addNewAddressButton);
        await clickElement(this.elements.addressLine1Input);
        await fillElement(this.elements.addressLine1Input, user.address.addressLine1);
        await clickElement(this.elements.addressLine2Input);
        await fillElement(this.elements.addressLine2Input, user.address.addressLine2);
        await clickElement(this.elements.cityInput);
        await fillElement(this.elements.cityInput, user.address.city);
        await clickElement(this.elements.postalCodeInput);
        await fillElement(this.elements.postalCodeInput, user.address.postcode);
        await clickElement(this.elements.stateProvinceButton);
        await clickElement(this.regManagerOption);
        if (addressType !== "Main") {
            const count = await this.elements.addressTypeButton.count();
            await clickElement(this.elements.addressTypeButton.nth(count - 1));
            await clickElement(this.phoneTypeOption(addressType));
        }
    }

    /**
     * Edits third party information for the user.
     * @param thirdParty - Third party name to select.
     * @param filteredApps - Array of filtered third party app names.
     */
    async editThirdParty(thirdParty: string, filteredApps: string[]): Promise<void> {
        await clickElement(this.elements.addThirdPartyButton);
        await clickElement(this.elements.editThirdPartyDropdown);
        await clickElement(this.thirdPartyOption(thirdParty));
        if (!filteredApps.includes(thirdParty)) {
            await clickElement(this.elements.editThirdPartyUsernameInput);
            await fillElement(this.elements.editThirdPartyUsernameInput, "ddd");
        }
    }

    /**
     * Verifies user details on the page.
     * @param user - User object containing details to verify.
     */
    async verifyUserDetails(user: User): Promise<void> {
        for (const key in user) {
            if (key === 'password' || key === 'phoneno') continue;
            if (typeof user[key] !== 'object') {
                await expect(this.verifyText(user[key])).toContainText(user[key], { timeout: 5000 });
            } else {
                for (const subKey in user[key]) {
                    if (user[key].hasOwnProperty(subKey)) {
                        await expect(this.verifyText(user[key][subKey])).toContainText(user[key][subKey], { timeout: 5000 });
                    }
                }
            }
        }
    }

    /**
     * Edits a user by updating details, roles, contact info, and third party info, then saves the user.
     * @param params - Object containing company, role, phoneType, addressType, thirdParty, and functionalArea.
     */
    async editUser(params: { company: string; role: string; phoneType: string; addressType: string; thirdParty: string; functionalArea: string; }): Promise<void> {
        const user: User = generateUser();
        await clickElement(this.elements.userGridEditButton);
        await this.editUserDetails(user, params.company, params.functionalArea);
        await this.editUserRoles(params.role);
        await this.editContactDetails(user, params.phoneType, params.addressType);
        await this.editThirdParty(params.thirdParty, fixture.commonContext.filteredApps || []);
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("User updated successfully");
        user.role = params.role;
        user.addressType = params.addressType;
        user.phoneType = params.phoneType;
        user.thirdparty = params.thirdParty;
        user.company = params.company;
        user.functionalArea = params.functionalArea.split(", ").sort().join(", ");
        fixture.commonContext.users.push({ username: user.username, password: user.password });
        // Optionally verify user
    }

}