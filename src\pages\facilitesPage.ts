import { expect, Page } from "@playwright/test";
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { FaciltyGroup, Facility } from "../helper/types/user";
import { generatefacilitygroup, generatefacility } from "../helper/util/common";
import { clickElement, fillElement, forceClickElement, waitAndClickElement } from "../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";
import Assert from "../helper/wrapper/assert";
let assert: Assert;

/**
 * Page Object Model for Facilities Page.
 * Provides methods to interact with and verify UI elements, perform CRUD operations on facility groups and facilities,
 * apply filters, validate grid data, and handle popups.
 */
export default class FacilitiesPage {
    private base: PlaywrightWrapper;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        assert = new Assert(page);
    }

    // Locators for Facilities Page elements
    private elements = {
        facilitiesTab: this.page.getByRole('link', { name: 'Facilities' }),
        addNewFacilityGroupButton: this.page.getByRole('button', { name: 'Add New Facility Group' }),
        groupNameInput: this.page.getByRole('textbox', { name: 'Group Name' }),
        companyDropdown: this.page.locator('div').filter({ hasText: /^Brand\*Select one$/ }).getByLabel('Select one'),
        companyDropdownold: this.page.locator('div[class$=px-5] button[aria-label="Select one"]'),
        submitButton: this.page.getByRole('button', { name: 'Submit' }),
        editButton: this.page.getByRole('button', { name: 'Edit' }),
        saveButton: this.page.getByRole('button', { name: 'Save' }),
        editBrandButton: this.page.getByRole('button', { name: /QUASEP|GESPRA|CPS|Instructor/, exact: true }),
        tableRows: this.page.locator('div[data-slot="table-container"] tbody>tr'),
        gridMessage: this.page.locator('.flex>img+p').first(),
        disableElements: this.page.locator('div[data-state="open"] input[data-slot="input"]:disabled'),
        facilityGridEditButton: this.page.locator('tbody tr:nth-child(2) td:nth-child(6) button[aria-label="Edit Facility"]'),
        facilityGridViewButton: this.page.locator('tbody tr:nth-child(2) td:nth-child(6) button[aria-label="View Facility"]'),
        getSearchOption: this.page.locator('input[data-slot="command-input"]'),
    };

    private filterElements = {
        brandDropdown: this.page.getByRole('combobox', { name: 'Brand' }),
        facilityGroupInput: this.page.getByRole('textbox', { name: 'Facility Group' }),
        searchButton: this.page.getByRole('button', { name: 'Search' }),
        resetButton: this.page.getByRole('button', { name: 'Reset' }),
        facilityRadioButton: this.page.locator('#facilities'),
        clientNumberInput: this.page.getByRole('textbox', { name: 'Client Number' }),
        divisionNumberInput: this.page.getByRole('textbox', { name: 'Division Number' }),
        facilityInput: this.page.getByRole('textbox', { name: 'Facility' }),
        statusActiveCheckbox: this.page.getByRole('checkbox', { name: 'Active', exact: true }),
        statusInactiveCheckbox: this.page.getByRole('checkbox', { name: 'Inactive', exact: true }),
        collapsibleButton: this.page.locator('div>div[data-slot="collapsible"]>button'),
    };

    private facilityElements = {
        addNewFacilityButton: this.page.getByRole('button', { name: 'Add New Facility', exact: true }),
        facilityNameInput: this.page.getByRole('textbox', { name: 'Facility Name' }),
        facilityGroupDropdown: this.page.locator('div').filter({ hasText: /^Facility Group\*Select one$/ }).getByLabel('Select one'),
        accountManagerDropdown: this.page.locator('div').filter({ hasText: /^Account Manager\*Select one$/ }).getByLabel('Select one'),
        facilityTypeDropdown: this.page.locator('div').filter({ hasText: /^Facility Type\*Select one$/ }).getByLabel('Select one'),
        divisionNumberInput: this.page.getByRole('textbox', { name: 'Division Number' }),
        clientNumberInput: this.page.getByRole('textbox', { name: 'Client Number' }),
        addressTypeDropdown: this.page.locator('div').filter({ hasText: /^Address Type\*Select one$/ }).getByLabel('Select one'),
        countryDropdown: this.page.locator('div').filter({ hasText: /^Country\*Select one$/ }).getByLabel('Select one'),
        addressLine1Input: this.page.getByRole('textbox', { name: 'Address Line 1' }),
        addressLine2Input: this.page.getByRole('textbox', { name: 'Address Line 2' }),
        cityInput: this.page.getByRole('textbox', { name: 'City' }),
        postalCodeInput: this.page.getByRole('textbox', { name: 'Postal Code' }),
        stateProvinceButton: this.page.getByRole('button', { name: 'Select one' }),
        mainAddressTypeButton: this.page.locator('button[aria-label="Main"]'),
        sharedMenuToggle: this.page.locator('button[aria-label*="Share Menus"]'),

    };

    // Utility locators
    private companyOption = (str: string) => this.page.getByRole('menuitem', { name: str });
    private addressTypeOption = (str: string) => this.page.getByRole('menuitem', { name: str });
    private facilityGroupSelect = (i: number) => this.page.locator('div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]').nth(i);
    private fGoractOptSelect: string = 'div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]';
    private brandOptSelect: string = 'div[data-slot="dropdown-menu-item"]';
    private brandOptSelectFlt: string = 'div[data-slot="command-item"]';
    private facilitySelect = (i: number) => this.page.locator('div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]').nth(i);
    private accountManagerSelect = (i: number) => this.page.locator('div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]').nth(i);
    private countrySelect = this.page.locator('div[data-slot="dropdown-menu-item"]').nth(0);
    private provinceSelect = this.page.locator('div[data-slot="dropdown-menu-item"]').nth(1);
    private statustoggle = this.page.locator('.flex.flex-col.gap-4>div:nth-child(1) .grid button[role="switch"]');
    private editDropdownSelect = (j: number, i: number) => this.page.locator(`.flex.flex-col.gap-4>div:nth-child(${j}) .grid label+button`).nth(i);
    private tableRowSelector = 'div[data-slot="table-container"] tbody>tr';
    private siteAreaAccessSele = this.page.locator('div[class$=gap-5]>div');


    private getTableCell = (rowIndex: number, colIndex: number) =>
        this.page.locator(`div[data-slot="table-container"] tbody>tr:nth-child(${rowIndex})>td`).nth(colIndex);
    private getBrandOption = (brand: string) =>
        this.page.locator(`div[data-slot="command-group"] div[data-slot="command-item"]:has-text("${brand}")`);
    private actMngorFcrOption = (acctmgr: string) =>
        this.page.locator(`div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]:has-text("${acctmgr}")`);

    /**
     * Navigates to the Facilities page.
     */
    async navigateToFacilitiesPage(): Promise<void> {
        await this.elements.facilitiesTab.click();
    }

    /**
     * Edits facility group details.
     * @param facilityGroup - Facility group object containing details.
     * @param editBrand - Brand to select for editing.
     */
    async editFacilityGroupDetails(facilityGroup: FaciltyGroup, editBrand: string): Promise<void> {
        await fillElement(this.elements.groupNameInput, facilityGroup.groupname);
        await clickElement(this.elements.editBrandButton);
        await fixture.page.waitForSelector(this.brandOptSelect);
        await waitAndClickElement(this.companyOption(editBrand), 100);
    }

    /**
     * Adds facility group details.
     * @param facilityGroup - Facility group object containing details.
     * @param company - Company to select.
     */
    async addFacilityGroupDetails(facilityGroup: FaciltyGroup, company: string): Promise<void> {
        await fillElement(this.elements.groupNameInput, facilityGroup.groupname);
        await clickElement(this.elements.companyDropdown);
        await fixture.page.waitForSelector(this.brandOptSelect);
        await waitAndClickElement(this.companyOption(company), 100);
    }

    /**
     * Adds facility details.
     * @param facility - Facility object containing details.
     * @param company - Company to select.
     * @param addressType - Address type to select.
     */
    async addFacilityDetails(facility: Facility, company: string, addressType: string, actmgr?: string): Promise<void> {
        await fillElement(this.facilityElements.facilityNameInput, facility.facilityname);
        await clickElement(this.elements.companyDropdown);
        await fixture.page.waitForSelector(this.brandOptSelect);
        await waitAndClickElement(this.companyOption(company), 100);
        await waitAndClickElement(this.facilityElements.facilityGroupDropdown, 500);
        await fixture.page.waitForSelector(this.fGoractOptSelect);
        await clickElement(this.facilityGroupSelect(1));
        await waitAndClickElement(this.facilityElements.accountManagerDropdown, 300);
        await fixture.page.waitForSelector(this.fGoractOptSelect);
        if (actmgr) {
            await fillElement(this.elements.getSearchOption, actmgr);
            await setTimeout(400);
            await clickElement(this.actMngorFcrOption(actmgr));
        } else {
            await clickElement(this.accountManagerSelect(1));
        }
        await clickElement(this.facilityElements.facilityTypeDropdown);
        await clickElement(this.facilitySelect(1));
        await fillElement(this.facilityElements.divisionNumberInput, facility.divisionnumber.toString());
        await fillElement(this.facilityElements.clientNumberInput, facility.clientnumber.toString());
        await clickElement(this.facilityElements.addressTypeDropdown);
        await clickElement(this.addressTypeOption(addressType));
        await clickElement(this.facilityElements.countryDropdown);
        await clickElement(this.countrySelect);
        await fillElement(this.facilityElements.addressLine1Input, facility.address.addressLine1);
        await fillElement(this.facilityElements.addressLine2Input, facility.address.addressLine2);
        await fillElement(this.facilityElements.cityInput, facility.address.city);
        await fillElement(this.facilityElements.postalCodeInput, facility.address.postcode);
        await clickElement(this.facilityElements.stateProvinceButton);
        await clickElement(this.provinceSelect);
    }

    /**
     * Edits facility details.
     * @param facility - Facility object containing details.
     * @param company - Company to select.
     * @param addressType - Address type to select.
     */
    async editFacilityDetails(facility: Facility, company: string, addressType: string): Promise<void> {
        await fillElement(this.facilityElements.facilityNameInput, facility.facilityname);
        await clickElement(this.editDropdownSelect(1, 0));
        await fixture.page.waitForSelector(this.brandOptSelect);
        await waitAndClickElement(this.companyOption(company), 100);
        await waitAndClickElement(this.editDropdownSelect(1, 1), 500);
        await fixture.page.waitForSelector(this.brandOptSelect);
        await clickElement(this.facilityGroupSelect(1));
        await waitAndClickElement(this.editDropdownSelect(1, 2), 300);
        await fixture.page.waitForSelector(this.brandOptSelect);
        await clickElement(this.accountManagerSelect(1));
        await clickElement(this.editDropdownSelect(1, 3));
        await clickElement(this.facilitySelect(1));
        await fillElement(this.facilityElements.divisionNumberInput, facility.divisionnumber.toString());
        await fillElement(this.facilityElements.clientNumberInput, facility.clientnumber.toString());
        await clickElement(this.editDropdownSelect(3, 0));
        await clickElement(this.addressTypeOption(addressType));
        await clickElement(this.editDropdownSelect(3, 1));
        await clickElement(this.countrySelect);
        await fillElement(this.facilityElements.addressLine1Input, facility.address.addressLine1);
        await fillElement(this.facilityElements.addressLine2Input, facility.address.addressLine2);
        await fillElement(this.facilityElements.cityInput, facility.address.city);
        await fillElement(this.facilityElements.postalCodeInput, facility.address.postcode);
        await clickElement(this.editDropdownSelect(3, 2));
        await clickElement(this.provinceSelect);
    }

    /**
     * Creates a new facility group.
     * @param company - Company to select for the facility group.
     */
    async createFacilityGroup({ company }: { company: string }): Promise<void> {
        const facilityGroup: FaciltyGroup = generatefacilitygroup();
        await fixture.logger.info(JSON.stringify(facilityGroup));
        await this.elements.addNewFacilityGroupButton.click();
        await fillElement(this.elements.groupNameInput, facilityGroup.groupname);
        //await this.base.verifyunsavedChangePopup();
        await this.base.verifyResetPopup();
        await this.addFacilityGroupDetails(facilityGroup, company);
        await fixture.logger.info(JSON.stringify(facilityGroup));
        await clickElement(this.elements.submitButton);
        await this.base.verifyToast("Facility group created successfully");
        fixture.commonContext.faciltygroup.push({ groupname: facilityGroup.groupname });
        await waitAndClickElement(this.elements.facilitiesTab, 500);
    }

    /**
     * Creates a new facility.
     * @param company - Company to select for the facility.
     * @param addressType - Address type to select for the facility.
     */
    async createFacility({ company, addressType, accountManager }: { company: string; addressType: string; accountManager?: string }): Promise<void> {
        const facility: Facility = generatefacility();
        await fixture.logger.info(JSON.stringify(facility));
        await this.facilityElements.addNewFacilityButton.click();
        await fillElement(this.facilityElements.facilityNameInput, "testpopup");
        await this.base.verifyResetPopup();
        //await this.base.verifyunsavedChangePopup();
        if (accountManager) {
            await this.addFacilityDetails(facility, company, addressType, accountManager);
        } else {
            await this.addFacilityDetails(facility, company, addressType);
        }
        await fixture.logger.info(JSON.stringify(facility));
        await clickElement(this.elements.submitButton);
        await this.base.verifyToast("Facility created successfully");
        fixture.commonContext.facility.push({ facilityname: facility.facilityname, divisionnumber: facility.divisionnumber, clientnumber: facility.clientnumber, address: facility.address });
    }

    /**
     * Creates a facility and sets its status to inactive.
     */
    async createFacilityAndSetInactive(): Promise<void> {
        await this.createFacility({ company: "QUASEP", addressType: "Shipping" });
        await waitAndClickElement(this.elements.editButton, 500);
        await clickElement(this.statustoggle);
        await this.verifyInactivateFacilityAndAssociatedUsersPopup();
        await this.page.getByRole('button', { name: 'Close dialog' }).click();
        await this.page.locator('div').filter({ hasText: /^Active$/ }).getByRole('switch').click();
        await clickElement(this.page.getByRole('button', { name: 'Yes, Inactivate' }));
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Facility updated successfully");
        await setTimeout(500);
        await expect(this.page.getByRole('main')).toContainText('Inactive');
    }

    /**
     * Verifies the inactivate facility and associated users confirmation popup.
     */
    async verifyInactivateFacilityAndAssociatedUsersPopup(): Promise<void> {
        await this.base.verifyWithLogging(
            this.page.getByRole('button', { name: 'Close dialog' }),
            "Button 'Close dialog'"
        );
        await this.base.verifyWithLogging(
            this.page.getByRole('button', { name: 'Yes, Inactivate' }),
            "Button 'Yes Inactivate'"
        );
        await this.base.verifyWithLogging(
            this.page.getByRole('button', { name: 'No, Cancel' }),
            "Button 'No Cancel'"
        );
        await expect(this.page.getByLabel('Inactivate Facility and').locator('span')).toContainText('Inactivate Facility and associated users');
        await expect(this.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Are you sure you want to inactivate the Facility? This action shall set status of all users associated with the Facility as Inactive.');
    }

    /**
     * Verifies that disabled fields are present when editing a facility.
     */
    async verifyEditFacilityDisabledFields(): Promise<void> {
        await clickElement(this.elements.facilityGridEditButton);
        await this.base.verifyDisabledElementsCount(7, "External Facility details edit page", 'greaterThan');
    }

    /**
     * Edits an external facility and saves changes.
     */
    async editExternalFacility(): Promise<void> {
        await this.page.getByRole('switch', { name: 'DownloadMenu toggle' }).click();
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Facility updated successfully");
        await setTimeout(500);
    }

    /**
     * Creates, edits, views, and searches for a facility group.
     * @param company - Company to select for the facility group.
     */
    async createEditViewAndSearchFacilityGroup({ company }: { company: string }): Promise<void> {
        const facilityGroup: FaciltyGroup = generatefacilitygroup();
        await fixture.logger.info(JSON.stringify(facilityGroup));
        await this.elements.addNewFacilityGroupButton.click();
        await this.verifyCreateFacilityGroupPageValidations();
        await this.addFacilityGroupDetails(facilityGroup, company);
        await fixture.logger.info(JSON.stringify(facilityGroup));
        await clickElement(this.elements.submitButton);
        await this.base.verifyToast("Facility group created successfully");
        const updatedFacilityGroup: FaciltyGroup = generatefacilitygroup();
        await fixture.logger.info(JSON.stringify(updatedFacilityGroup));
        await waitAndClickElement(this.elements.editButton, 500);
        await setTimeout(500);
        await this.editFacilityGroupDetails(updatedFacilityGroup, company);
        await fixture.logger.info(JSON.stringify(updatedFacilityGroup));
        await clickElement(this.elements.editBrandButton);
        const secondOption = this.page.locator('div[data-slot="dropdown-menu-item"]').nth(1);
        const selectedBrand = await secondOption.innerText();
        await secondOption.click();
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Facility group updated successfully");
        await setTimeout(500);
        await this.verifyFacilityGroupDetailsInViewPage(updatedFacilityGroup, selectedBrand);
        await setTimeout(500);
        await this.elements.facilitiesTab.click();
        await setTimeout(1200);
        const stateVal = await this.filterElements.collapsibleButton.getAttribute("data-state");
        if (stateVal === "closed") {
            await clickElement(this.filterElements.collapsibleButton);
        }
        await clickElement(this.filterElements.resetButton);
        await this.filterElements.brandDropdown.click();
        await forceClickElement(this.getBrandOption(selectedBrand));
        //await this.page.getByText(selectedBrand, { exact: true }).click();
        await this.base.escapeOnDropdown();
        await this.filterElements.facilityGroupInput.fill(updatedFacilityGroup.groupname);
        await clickElement(this.filterElements.searchButton);
        await this.verifyFirstRowInFacilityGroupGrid(updatedFacilityGroup, selectedBrand);
    }

    /**
     * Creates, edits, views, and searches for a facility.
     * @param company - Company to select for the facility.
     * @param addressType - Address type to select for the facility.
     */
    async createEditViewAndSearchFacility({ company, addressType }: { company: string; addressType: string; }): Promise<void> {
        const facility: Facility = generatefacility();
        await this.facilityElements.addNewFacilityButton.click();
        await this.verifyCreateFacilityPageValidations();
        await this.addFacilityDetails(facility, company, addressType);
        await fixture.logger.info(JSON.stringify(facility));
        await clickElement(this.elements.submitButton);
        await this.base.verifyToast("Facility created successfully");
        const updatedFacility: Facility = generatefacility();
        await fixture.logger.info(JSON.stringify(updatedFacility));
        await clickElement(this.elements.editButton);
        await this.verifyEditFacilityPageValidations();
        await setTimeout(500);
        await this.editFacilityDetails(updatedFacility, company, addressType);
        await waitAndClickElement(this.elements.editBrandButton, 500);
        const secondOption = this.page.locator('div[data-slot="dropdown-menu-item"]').nth(1);
        const selectedBrand = await secondOption.innerText();
        await secondOption.click();
        await waitAndClickElement(this.editDropdownSelect(1, 1), 1000);
        await fixture.page.waitForSelector('div[data-slot="dropdown-menu-item"]');
        const secondOptionFG = this.page.locator('div[data-slot="dropdown-menu-item"]').nth(1);
        const selectedFG = await secondOptionFG.innerText();
        await secondOptionFG.click();
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Facility updated successfully");
        await setTimeout(500);
        await this.verifyFacilityDetailsInViewPage(updatedFacility, selectedBrand, selectedFG);
        await setTimeout(500);
        await clickElement(this.elements.facilitiesTab);
        await setTimeout(1200);
        const stateVal = await this.filterElements.collapsibleButton.getAttribute("data-state");
        if (stateVal === "closed") {
            await clickElement(this.filterElements.collapsibleButton);
        }
        await clickElement(this.filterElements.resetButton);
        await clickElement(this.filterElements.facilityRadioButton);
        await this.verifyFacilityFilterFields();
        await clickElement(this.filterElements.brandDropdown);
        await forceClickElement(this.getBrandOption(selectedBrand));
        //await this.page.getByText(selectedBrand, { exact: true }).click();
        await this.base.escapeOnDropdown();
        await fillElement(this.filterElements.facilityInput, updatedFacility.facilityname);
        await fillElement(this.filterElements.divisionNumberInput, updatedFacility.divisionnumber);
        await fillElement(this.filterElements.clientNumberInput, updatedFacility.clientnumber);
        await setTimeout(500);
        await clickElement(this.filterElements.searchButton);
        await this.base.waitForResponse("filter");
        await this.base.verifyWithLogging(
            this.page.getByRole('heading', { name: 'facilities' }),
            "Grid heading 'facilities'"
        );
        await this.verifyFirstRowInFacilityGrid(updatedFacility, selectedFG);
    }

    /**
     * Verifies facility filter fields are present.
     */
    async verifyFacilityFilterFields(): Promise<void> {
        await expect(this.page.getByRole('main')).toContainText('Brand');
        await expect(this.page.getByRole('main')).toContainText('Search By');
        await expect(this.page.getByRole('main')).toContainText('Facility');
        await expect(this.page.locator('legend')).toContainText('Status');
        await expect(this.page.getByRole('main')).toContainText('Division Number');
        await expect(this.page.getByRole('main')).toContainText('Client Number');
        await expect(this.page.getByRole('main')).toContainText('Filter By');
        await expect(this.page.getByRole('button', { name: 'Reset' })).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Search' })).toBeVisible();
        await expect(this.page.locator('#facility_groups')).toBeVisible();
        await expect(this.page.locator('#facilities')).toBeVisible();
        await expect(this.page.getByRole('radiogroup')).toContainText('Facility Group');
        await expect(this.page.getByRole('radiogroup')).toContainText('Facility');
    }

    /**
     * Verifies facility group details in the view page.
     * @param expected - Expected facility group object.
     * @param expectedBrand - Expected brand name.
     */
    async verifyFacilityGroupDetailsInViewPage(expected: FaciltyGroup, expectedBrand: string): Promise<void> {
        const groupNameLocator = this.page.getByText(expected.groupname, { exact: true });
        await expect(groupNameLocator).toBeVisible();
        const brandLocator = this.page.getByText(expectedBrand, { exact: true });
        await expect(brandLocator).toBeVisible();
    }

    //create FG page details
    async verifyCreateFacilityGroupPageValidations() {

        // Reusable Page Verification for "Add New Facility Group"
        await this.verifyPageUI({
            headingText: "Add New Facility Group",

            // Static texts that should exist inside <main>
            requiredMainTexts: [
                "Facility Group Details",
                "Site Area Access",
                "Admin Notes",
                "DownloadMenu",
                "PrintRecipe",
                "ScaleRecipe",
                "RecipeBinder",
                "DownloadTheme",
                "DownloadEducation",
                "ePurchasing",
                "Dashboard",
                "ImportRecipe",
                "HC Procurement",
                "ePurchasing v2-Staging",
                "HUBERT",
                "Enable RDS Sync",
                "PartsTown",
                "Notes",
                "Source",
                "Group Name",
                "Status",
                "Brand"
            ],

            // Buttons + specific elements
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
                { locator: this.page.getByRole('button', { name: 'Submit' }), label: "Button 'Submit'" },
                { locator: this.page.getByRole('button', { name: 'Facility Group Details' }), label: "Button 'Facility Group Details'" },
                { locator: this.page.getByRole('button', { name: 'Site Area Access' }), label: "Button 'Site Area Access'" },
                { locator: this.page.getByRole('button', { name: 'Admin Notes' }), label: "Button 'Admin Notes'" },
                { locator: this.page.locator('div').filter({ hasText: /^Active$/ }).getByRole('switch'), label: "Active switch" },
            ]
        });

        //validate brand options
        await this.base.validateDropdownOptions(
            this.elements.companyDropdown,
            ["CPS", "GESPRA", "QUASEP", "Instructor"]
        );
        await this.base.escapeOnDropdown();

        await setTimeout(300);

        // 🔄 Action + post-validation (submit click & error message checks)
        await this.page.getByRole('button', { name: 'Submit' }).click();
        // await expect(this.page.getByRole('alert')).toContainText(
        //     'Please enter valid data before submitting the form.'
        // );
        await this.base.verifyToast('Please enter valid data before submitting the form.')
        await expect(this.page.getByRole('main')).toContainText('Group Name is required');
        await expect(this.page.getByRole('main')).toContainText('Brand is required');

        await this.page.getByRole('button', { name: 'Reset Changes' }).click();
        await this.page.getByRole('button', { name: 'Yes, Proceed' }).click();
        await this.base.verifyResetPopup();

    }
    //create Facility page details
    async verifyCreateFacilityPageValidations() {

        // Reusable Page Verification for "Add New Facility Group"
        await this.verifyPageUI({
            headingText: "Add New Facility",

            // Static texts that should exist inside <main>
            requiredMainTexts: [
                "Facility Details",
                "Site Area Access",
                "Admin Notes",
                "Address",
                "DownloadMenu",
                "PrintRecipe",
                "ScaleRecipe",
                "RecipeBinder",
                "DownloadTheme",
                "DownloadEducation",
                "ePurchasing",
                "Dashboard",
                "ImportRecipe",
                "HC Procurement",
                "ePurchasing v2-Staging",
                "HUBERT",
                "Enable RDS Sync",
                "PartsTown",
                "Notes",
                "Source",
                "Facility Name",
                "Status",
                "Facility Group",
                "Account Manager",
                "Facility Type",
                "Division Number",
                "Client Number",
                "Logo File",
                "Share Menus Outside of Primary Facility Group",
                "Brand",
                "Address Type",
                "Country",
                "Address Line 1",
                "Address Line 2",
                "City",
                "Province/State",
                "Postal Code"
            ],

            // Buttons + specific elements
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
                { locator: this.page.getByRole('button', { name: 'Submit' }), label: "Button 'Submit'" },
                { locator: this.page.getByRole('button', { name: 'Facility Details' }), label: "Button 'Facility Details'" },
                { locator: this.page.getByRole('button', { name: 'Site Area Access' }), label: "Button 'Site Area Access'" },
                { locator: this.page.getByRole('button', { name: 'Address' }), label: "Button 'Address'" },
                { locator: this.page.getByRole('button', { name: 'Admin Notes' }), label: "Button 'Admin Notes'" },
                { locator: this.page.locator('div').filter({ hasText: /^Active$/ }).getByRole('switch'), label: "Active switch" },
                { locator: this.page.locator('button[aria-label*="Share Menus"]'), label: "Shared menu Outside switch" },
            ]
        });

        // shared menu toggle default attribute 
        await expect(this.facilityElements.sharedMenuToggle).toHaveAttribute('aria-checked', 'false');

        //validate brand options
        await this.base.validateDropdownOptions(
            this.elements.companyDropdown,
            ["CPS", "GESPRA", "QUASEP", "Instructor"]
        );
        await this.base.escapeOnDropdown();

        await setTimeout(300);

        //validate address type drop down options and order
        await this.base.validateDropdownOptions(
            this.facilityElements.addressTypeDropdown,
            ["Main", "Other", "Shipping", "Business", "Billing"]
        );
        await this.base.escapeOnDropdown();

        // Action + post-validation (submit click & error message checks)
        await this.page.getByRole('button', { name: 'Submit' }).click();
        // await expect(this.page.getByRole('alert')).toContainText(
        //     'Please enter valid data before submitting the form.'
        // );
        await this.base.verifyToast('Please enter valid data before submitting the form.')

        // Define all required error texts
        const requiredMainErrorTexts: string[] = [
            'Facility name is required',
            'Brand is required',
            'Facility group is required',
            'Account manager is required',
            'Facility type is required',
            'Division number is required',
            'Client number is required',
            'Address type is required',
            'Country is required',
            'Address line 1 is required',
            'City is required',
            'Province is required',
            'Postal code is required'
        ];

        // Loop through the texts and assert visibility
        for (const text of requiredMainErrorTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            fixture.logger.info(`Main text "${text}" is visible`);
        }
        // await this.page.getByRole('button', { name: 'Reset Changes' }).click();
        // await this.page.getByRole('button', { name: 'Yes, Proceed' }).click();
        //validate country drop down options and order
        await this.base.validateDropdownOptions(
            this.page.locator('div').filter({ hasText: /^Country\*Select oneCountry is required$/ }).getByLabel('Select one'),
            ["Canada", "USA"],
            true
        );

        await this.page.getByRole('menuitem', { name: 'USA' }).click();
        await setTimeout(300);

        //validate proviounce based on country selection options and order
        await this.base.validateDropdownOptions(
            this.page.locator('div').filter({ hasText: /^Province\/State\*Select oneProvince is required$/ }).getByLabel('Select one'),
            [
                "Pennsylvania",
                "FLORIDA",
                "GEORGIA",
                "HAWAII",
                "IOWA",
                "IDAHO",
                "ILLINOIS",
                "INDIANA",
                "KANSAS",
                "KENTUCKY",
                "LOUISIANA",
                "MASSACHUSETTS",
                "MARYLAND",
                "MAINE",
                "MICHIGAN",
                "MINNESOTA",
                "MISSOURI",
                "MISSISSIPPI",
                "MONTANA",
                "NEBRASKA",
                "NORTH CAROLINA",
                "NORTH DAKOTA",
                "NEW HAMPSHIRE",
                "NEW JERSEY",
                "NEW MEXICO",
                "NEW YORK",
                "NEVADA",
                "OHIO",
                "OKLAHOMA",
                "RHODE ISLAND",
                "SOUTH CAROLINA",
                "SOUTH DAKOTA",
                "TENNESSEE",
                "TEXAS",
                "UTAH",
                "VIRGINIA",
                "VERMONT",
                "WASHINGTON",
                "WISCONSIN",
                "OREGON",
                "WEST VIRGINIA",
                "WYOMING",
                "ALASKA",
                "ALABAMA",
                "ARKANSAS",
                "ARIZONA",
                "CALIFORNIA",
                "COLORADO",
                "CONNECTICUT",
                "DELAWARE"
            ]
        );

        await this.base.escapeOnDropdown();
        await setTimeout(300);
        await clickElement(this.page.getByRole('button', { name: 'USA' }));
        await this.page.getByRole('menuitem', { name: 'Canada' }).click();
        //validate proviance basedon country canada drop down options and order
        await this.base.validateDropdownOptions(
            this.page.locator('div').filter({ hasText: /^Province\/State\*Select oneProvince is required$/ }).getByLabel('Select one'),
            [
                "Alberta",
                "British Columbia",
                "Manitoba",
                "New Brunswick",
                "Newfoundland and Labrador",
                "Nova Scotia",
                "Northwest Territories",
                "Nunavut",
                "Ontario",
                "Prince Edward Island",
                "Quebec",
                "Saskatchewan",
                "Yukon"
            ]
        );

        await this.base.escapeOnDropdown();
        //await this.base.verifyunsavedChangePopup();
        await this.page.getByRole('button', { name: 'Reset Changes' }).click();
        await this.page.getByRole('button', { name: 'Yes, Proceed' }).click();
        await this.base.verifyResetPopup();
    }

    //common verication page defaults

    async verifyPageUI({
        headingText,
        requiredMainTexts = [],
        commonElements = [],
        pageSpecificElements = []
    }: {
        headingText: string;
        requiredMainTexts?: string[];
        commonElements?: { locator: any; label: string }[];
        pageSpecificElements?: { locator: any; label: string }[];
    }) {
        // Verify heading text
        await expect(this.page.getByRole('heading')).toContainText(headingText);
        //fixture.logger.info(` Heading "${headingText}" is visible`);

        //  Verify additional static texts inside main
        for (const text of requiredMainTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            //fixture.logger.info(` Main text "${text}" is visible`);
        }
        // for create page common toggle items
        for (const toggle of await this.siteAreaAccessSele.all()) {
            const label = (await toggle.locator('p').innerText()).trim().toLowerCase();
            const checkbox = toggle.locator('button');

            if (label === "importrecipe" || label === "enable rds sync") {
                await expect(checkbox).toHaveAttribute('aria-checked', 'false');
                //fixture.logger.info(`Label ${label} value is false`);
            } else {
                await expect(checkbox).toHaveAttribute('aria-checked', 'true');
                //fixture.logger.info(`Label ${label} value is true`);
            }
        }
        await expect(this.statustoggle).toHaveAttribute('aria-checked', 'true');

        //  Define default common elements (can be overridden if passed in)
        const defaultCommonElements = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
        ];

        //  Merge everything
        const allElements = [...defaultCommonElements, ...commonElements, ...pageSpecificElements];

        //  Verify all UI elements with logging
        for (const { locator, label } of allElements) {
            await this.base.verifyWithLogging(locator, label);
        }
    }



    /**
     * Verifies facility details in the view page.
     * @param expected - Expected facility object.
     * @param expectedBrand - Expected brand name.
     * @param expectedFG - Expected facility group name.
     */
    async verifyFacilityDetailsInViewPage(expected: Facility, expectedBrand: string, expectedFG: string): Promise<void> {
        const facilityNameLocator = this.page.getByText(expected.facilityname, { exact: true });
        await expect(facilityNameLocator).toBeVisible();
        const brandLocator = this.page.getByText(expectedBrand, { exact: true });
        await expect(brandLocator).toBeVisible();
        const fgLocator = this.page.getByText(expectedFG, { exact: true });
        await expect(fgLocator).toBeVisible();
    }


    async verifyEditFacilityPageValidations() {

        //Edit Facility
        await this.verifyPageUI({
            headingText: "Edit Facility",

            // Static texts that should exist inside <main>
            requiredMainTexts: [
                "Facility Details",
                "Site Area Access",
                "Admin Notes",
                "Address",
                "DownloadMenu",
                "PrintRecipe",
                "ScaleRecipe",
                "RecipeBinder",
                "DownloadTheme",
                "DownloadEducation",
                "ePurchasing",
                "Dashboard",
                "ImportRecipe",
                "HC Procurement",
                "ePurchasing v2-Staging",
                "HUBERT",
                "Enable RDS Sync",
                "PartsTown",
                "Notes",
                "Source",
                "Facility Name",
                "Brand",
                "Facility Group",
                "Status",
                "Account Manager",
                "Facility Type",
                "Division Number",
                "Client Number",
                "Logo File",
                "Created Date",
                "Created By",
                "Last Modified Date",
                "Last Modified By",
                "Share Menus Outside of Primary Facility Group",
                "Address Type",
                "Country",
                "Address Line 1",
                "Address Line 2",
                "City",
                "Province/State",
                "Postal Code"
            ],

            // Buttons + specific elements
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
                { locator: this.page.getByRole('button', { name: 'Save' }), label: "Button 'Save'" },
                { locator: this.page.getByRole('button', { name: 'Facility Details' }), label: "Button 'Facility Details'" },
                { locator: this.page.getByRole('button', { name: 'Site Area Access' }), label: "Button 'Site Area Access'" },
                { locator: this.page.getByRole('button', { name: 'Address' }), label: "Button 'Address'" },
                { locator: this.page.getByRole('button', { name: 'Admin Notes' }), label: "Button 'Admin Notes'" },
                { locator: this.page.locator('div').filter({ hasText: /^Active$/ }).getByRole('switch'), label: "Active switch" },
                { locator: this.page.locator('button[aria-label*="Share Menus"]'), label: "Shared menu Outside switch" },
            ]
        });
        await this.base.verifyDisabledElementsCount(5, "Edit Facility page", "equal");

    }

    /**
     * Verifies the first row in the facility group grid.
     * @param expected - Expected facility group object.
     * @param expectedBrand - Expected brand name.
     */
    async verifyFirstRowInFacilityGroupGrid(expected: FaciltyGroup, expectedBrand?: string): Promise<void> {
        const firstRow = this.page.locator('table tbody tr:nth-child(1)');
        const groupNameCell = firstRow.locator('td').nth(0);
        await expect(groupNameCell).toHaveText(expected.groupname);
        if (expectedBrand !== undefined) {
            const brandCell = firstRow.locator('td').nth(1);
            await expect(brandCell).toHaveText(expectedBrand);
        }
        await expect(firstRow.getByLabel('View Facility Group').first()).toBeVisible();
        await expect(firstRow.getByLabel('Edit Facility Group').first()).toBeVisible();
    }

    /**
     * Verifies the first row in the facility grid.
     * @param expected - Expected facility object.
     * @param expectedFg - Expected facility group name.
     */
    async verifyFirstRowInFacilityGrid(expected: Facility, expectedFg?: string): Promise<void> {
        const firstRow = this.page.locator('table tbody tr:nth-child(1)');
        const facilityNameCell = firstRow.locator('td').nth(0);
        await expect(facilityNameCell).toHaveText(expected.facilityname);
        if (expectedFg !== undefined) {
            const fgCell = firstRow.locator('td').nth(1);
            await expect(fgCell).toHaveText(expectedFg);
        }
        const divisionNumberCell = firstRow.locator('td').nth(2);
        await expect(divisionNumberCell).toHaveText(expected.divisionnumber.toString());
        await expect(firstRow.getByLabel('View Facility').first()).toBeVisible();
        await expect(firstRow.getByLabel('Edit Facility').first()).toBeVisible();
    }

    /**
     * Verifies facility group grid column names and pagination.
     * @param expectedHeaders - Array of expected header strings.
     */
    async verifyFacilityGroupGridColumnNames(expectedHeaders: string[]): Promise<void> {
        //await clickElement(this.filterElements.searchButton);
        await this.verifyFilterByButtonValidation();
        await this.applyFacilityAndGroupFilter({
            filterName: 'Brand',
            filterValue: 'CPS'

        });
        await this.base.verifyGridColumnNames(expectedHeaders);
        await this.base.verifyPaginationText();
    }


    //filter by buttons validation

    async verifyFilterByButtonValidation() {

        await assert.assertElementHasText(this.page.getByRole('button', { name: 'Filter By' }), "Filter By");
        await assert.assertElementHasAttributeValue(this.filterElements.collapsibleButton, 'data-state', 'open');
        await assert.assertElementHasText(this.page.getByRole('heading', { name: 'facility groups' }),
            'facility groups');
        await assert.assertElementHasText(this.elements.gridMessage, 'Please apply filters to view data.');
    }

    /**
     * Verifies facilities grid column names and pagination.
     * @param expectedHeaders - Array of expected header strings.
     */
    async verifyFacilitiesGridColumnNames(expectedHeaders: string[]): Promise<void> {
        await this.applyFacilityAndGroupFilter({
            filterName: 'Facility',
            filterValue: 'test'
        });
        await this.base.verifyGridColumnNames(expectedHeaders);
        await this.base.verifyPaginationText();

    }


    // create and search for new facility or Fg

    async searchForNewFacility() {
        await this.navigateToFacilitiesPage();
        //console.log(fixture.commonContext.getLatestFacility());
        var latestfacility = fixture.commonContext.getLatestFacility();
        await this.applyFacilityAndGroupFilter({
            filterName: 'Facility',
            filterValue: latestfacility.facilityname

        });
        await this.verifyFirstRowInFacilityGrid(latestfacility);

    }

    // create and search for new facilityGroup
    async searchForFacilityGroup() {
        await this.navigateToFacilitiesPage();
        //console.log(fixture.commonContext.getLatestFacility());
        var latestfacilityGroup = fixture.commonContext.getLatestFacilityGroup();
        await this.applyFacilityAndGroupFilter({
            filterName: 'FacilityGroup',
            filterValue: latestfacilityGroup.groupname

        });

        await this.verifyFirstRowInFacilityGroupGrid(latestfacilityGroup);

    }

    /**
     * Applies filters in the search area for facilities and facility groups.
     * Supports single and multi-filter scenarios.
     * @param filterName - The name of the filter to apply.
     * @param filterValue - The value to filter by.
     */
    async applyFacilityAndGroupFilter(
        { filterName, filterValue }: { filterName: string; filterValue: string; }
    ): Promise<void> {
        const multiFilterEnabled = filterName === "MultiFilter";
        const applyFilter = async (name: string, value: string) => {
            switch (name) {
                case 'Brand':
                    await clickElement(this.filterElements.brandDropdown);
                    await fixture.page.waitForSelector(this.brandOptSelectFlt);
                    await forceClickElement(this.getBrandOption(value));
                    await this.base.escapeOnDropdown();
                    break;
                case 'Facility':
                    await clickElement(this.filterElements.facilityRadioButton);
                    await setTimeout(400);
                    await fillElement(this.filterElements.facilityInput, value);
                    break;
                case 'ClientNumber':
                    await clickElement(this.filterElements.facilityRadioButton);
                    await setTimeout(400);
                    await fillElement(this.filterElements.clientNumberInput, value);
                    break;
                case 'DivisionNumber':
                    await clickElement(this.filterElements.facilityRadioButton);
                    await setTimeout(400);
                    await fillElement(this.filterElements.divisionNumberInput, value);
                    break;
                case 'FacilityGroup':
                    await fillElement(this.filterElements.facilityGroupInput, value);
                    break;
                case 'Status':
                    await forceClickElement(
                        value === 'Active' ? this.filterElements.statusActiveCheckbox : this.filterElements.statusInactiveCheckbox
                    );
                    break;
                default:
                    fixture.logger.warn(`Unknown filter: ${name}`);
            }
        };

        if (multiFilterEnabled) {
            const filters = filterValue.split(',').map(f => f.trim());
            for (const f of filters) {
                const [name, value] = f.split(':').map(s => s.trim());
                await applyFilter(name, value);
                fixture.logger.warn(`applying filter: ${name}, and value: ${value}`);
            }
            await setTimeout(1500);
        } else {
            await applyFilter(filterName, filterValue);
            fixture.logger.warn(`applying filter: ${filterName}, and value: ${filterValue}`);
        }

        await clickElement(this.filterElements.searchButton);
        try {
            await this.base.waitForResponse("filter");
            await setTimeout(500);
            await this.base.waitForSelectorVisible(this.tableRowSelector);
            const rowCount = await this.elements.tableRows.count();
            fixture.logger.info(`Filter By : ${filterName} :Row count in grid: ${rowCount}`);
            await assert.assertToBeGreaterThanEqual(rowCount, 1);
        } catch (error) {
            const gridMessageText = await this.elements.gridMessage.innerText();
            fixture.logger.info(`Grid message: ${gridMessageText}`);
            await assert.assertStringContaining(gridMessageText.trim(), "No data found");
        }
        await setTimeout(1000);
        const rowCount = await this.elements.tableRows.count();
        if (rowCount > 1) {
            await this.base.verifyPaginationText();
            if (!multiFilterEnabled) {
                await this.verifyFacilityAndGroupGridData({ filterName, filterValue });
            }
        }
        const stateVal = await this.filterElements.collapsibleButton.getAttribute("data-state");
        if (stateVal === "closed") {
            await clickElement(this.filterElements.collapsibleButton);
        }
        await clickElement(this.filterElements.resetButton);
    }

    /**
     * Verifies grid data and pagination for the applied filter in facilities and facility groups.
     * @param params - Object containing filterName, filterValue, and optional rowCount.
     */
    async verifyFacilityAndGroupGridData(
        {
            filterName,
            filterValue,
            rowCount = 1
        }: { filterName: string; filterValue: string; rowCount?: number }
    ): Promise<void> {
        const columnMap: Record<string, number> = {
            'ClientNumber': 3,
            'DivisionNumber': 2,
            'Facility': 0,
            'Brand': 1,
            'FacilityGroup': 0
        };

        for (let row = 1; row <= rowCount; row++) {
            if (columnMap[filterName]) {
                const cellText = await this.getTableCell(row, columnMap[filterName]).innerText();
                fixture.logger.info(
                    `Row ${row}, Col ${columnMap[filterName]}: got [${cellText}] vs Expected [${filterValue}]`
                );
                await assert.assertStringContainingIgnoreCase(cellText, filterValue);
                continue;
            }
        }

        if (filterName !== 'Multiunit' && filterName == '3rdPartyApp') {
            fixture.logger.warn(`Unknown filter: ${filterName}`);
        }
    }
}
