import { expect, Page } from "@playwright/test";
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { CreatedUser, ExtUser, User } from "../helper/types/user";
import { generateUser } from "../helper/util/common";
import { clickElement, fillElement, forceClickElement, waitAndClickElement } from "../helper/wrapper/actions";
import Assert from "../helper/wrapper/assert";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";
import { options } from "../helper/util/logger";
import { Roles } from "../helper/types/role";
let assert: Assert;

export default class RegisterPage {

    private base: PlaywrightWrapper;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        assert = new Assert(page);
    }

    // Locators for Register Page elements
    private elements = {
        internal: this.page.locator('a').filter({ hasText: /^Internal\*$/ }),
        newInternal: this.page.locator('a').filter({ hasText: /^Manage User - New\*$/ }),
        addNewUser: this.page.locator('button[aria-label="Add New User"]'),
        firstNameInput: this.page.getByRole('textbox', { name: 'First Name' }),
        lastNameInput: this.page.getByRole('textbox', { name: 'Last Name' }),
        companyDropdown: this.page.locator('div').filter({ hasText: /^Brand\*Select one$/ }).getByLabel('Select one'),
        languageDropdown: this.page.locator('div').filter({ hasText: /^Language\*Select one$/ }).getByRole('button').nth(1),
        languageSelect: this.page.getByRole('option', { name: 'English' }),
        statusSwitch: this.page.getByRole('switch'),
        positionInput: this.page.getByRole('textbox', { name: 'Position' }),
        facilityGroupDropdown: this.page.getByTitle('combobox'),
        principalFacilityGroupDropdown: this.page.locator('div').filter({ hasText: /^Principal Facility Group\*Select one$/ }).getByLabel('Select one'),
        principalFacilityDropdown: this.page.locator('div').filter({ hasText: /^Principal Facility\*Select one$/ }),
        functionalAreaDropdown: this.page.getByRole('combobox', { name: 'Functional Area' }),
        clearFunctionalAreaButton: this.page.getByRole('button', { name: 'Clear Selected' }),
        functionalAreaSelect: this.page.getByText('Administration'),
        usernameInput: this.page.getByRole('textbox', { name: 'Username' }),
        passwordInput: this.page.getByRole('textbox', { name: 'Password' }),
        roleSelectionButton: this.page.getByRole('button', { name: 'Select one' }).first(),
        roleSelectionButton1: this.page.locator("div[class$='items-baseline'] button").first(),
        editRoleSelectionButton: this.page.locator('[class~="items-baseline"] div:nth-child(1) label~button'),
        emailInput: this.page.getByRole('textbox', { name: 'Email' }),
        addNewPhoneButton: this.page.locator('div').filter({ hasText: /^Phone Numbers\+ ADD NEW$/ }).getByLabel('+ ADD NEW'),
        phoneNumberInput: this.page.getByRole('textbox', { name: 'Phone Number' }),
        phoneTypeButton: this.page.locator('button[name="contactDetails.phoneNumbers.0.phoneType"]'),
        addNewAddressButton: this.page.locator('div').filter({ hasText: /^Addresses\+ ADD NEW$/ }).getByLabel('+ ADD NEW'),
        addressLine1Input: this.page.getByRole('textbox', { name: 'Address Line 1' }),
        addressLine2Input: this.page.getByRole('textbox', { name: 'Address Line 2' }),
        cityInput: this.page.getByRole('textbox', { name: 'City' }),
        postalCodeInput: this.page.getByRole('textbox', { name: 'Postal Code' }),
        stateProvinceButton: this.page.getByRole('button', { name: 'Select one' }).first(),
        addressTypeButton: this.page.locator('button[aria-label="Main"]'),
        addThirdPartyButton: this.page.getByRole('button', { name: 'Add New', exact: true }),
        thirdPartyOptionButton: this.page.getByRole('button', { name: 'Select one' }).first(),
        editThirdPartyOptionButton: this.page.getByRole('cell', { name: 'Select one' }).getByLabel('Select one'),
        thirdPartyUsernameInput: this.page.getByRole('textbox', { name: 'Username', exact: true }),
        thirdPartyUsernameInputAlt: this.page.locator('input[aria-label="Username"]'),
        submitButton: this.page.getByRole('button', { name: 'Submit' }),
        resetButton: this.page.getByRole('button', { name: 'Reset' }),
        accountManagerButton: this.page.locator('button[name="roleSelection\\.regionalDirector"]'),
        accountManagerButtonAlt: this.page.locator('button[name="roleSelection.accountManager"]'),
        primaryProvinceButton: this.page.locator('button[name="roleSelection\\.primaryProvince"]'),
        secondaryProvinceButton: this.page.getByLabel('Additional Provinces'),
        instructorPortalDepartmentButton: this.page.locator('button[aria-label="Instructor Portal Department"]'),
        instructorPortalFacilityButton: this.page.locator('button[aria-label="Instructor Portal Facility"]'),
        instructorPortalTypeButton: this.page.getByLabel('Suggestions').getByText('Administration'),
        cmfaCheckbox: this.page.getByLabel('CMFA'),
        confirmInactiveButton: this.page.getByRole('button', { name: 'Yes, Continue' }),
        userNameInput: this.page.getByRole('textbox', { name: 'Username' }),
        usersearchButton: this.page.getByRole('button', { name: 'Search' }),
        userHomeLink: this.page.getByRole('link', { name: 'Users' }),
        userEditButton: this.page.getByRole('button', { name: 'Edit' }),
        userGridEditButton: this.page.locator('tbody tr:nth-child(1) td:nth-child(8) button[aria-label="Edit User"]'),
        userGridViewButton: this.page.locator('tbody tr:nth-child(1) td:nth-child(8) button[aria-label="View User"]'),
        editCompanyDropdown: this.page.locator('text=QUASEP').or(this.page.locator('text=GESPRA')).or(this.page.getByRole('button', { name: 'CPS', exact: true })),
        editThirdPartyUsernameInput: this.page.locator('input[name="authorisations.0.username"]'),
        saveButton: this.page.getByRole('button', { name: 'Save' }),
        editUsernameInput: this.page.locator('input[data-slot="input"][placeholder="Enter username"]'),
        disabledElements: this.page.locator('div[data-state="open"] input[data-slot="input"]:disabled'),
        deleteContactButton: this.page.locator('.px-4.py-1>div>table button[aria-label="Delete item"]'),
        deleteThirdPartyButton: this.page.locator('div[data-state="open"]:nth-child(4)>div>div table>tr>td button[data-slot="button"]'),
    };

    private companyOption = (name: string) => this.page.getByRole('menuitem', { name });
    private verifyText = (text: string) => this.page.getByText(text).first();
    private verifyExactText = (text: string) => this.page.getByText(text, { exact: true }).first();
    private verifyButton = (name: string) => this.page.getByRole('button', { name });
    private addPhoneType = (type: string) => this.page.getByRole('menuitem', { name: type, exact: true });
    private roleSelect = (role: string) => this.page.getByRole('menuitem', { name: role, exact: true });
    private thirdPartySelect = (name: string) => this.page.getByRole('menuitem', { name, exact: true });
    private newRoleNameOpt = (name: string) => this.page.getByRole('checkbox', { name: `${name} checkbox`, exact: true });
    private regMan = this.page.locator('div[data-slot="dropdown-menu-content"] div[data-slot="dropdown-menu-item"]').nth(0);
    private fgOptionSelector: string = 'div[data-slot="command"] div[data-slot="command-list"] div[data-slot="dropdown-menu-item"]';
    private facilityGroupSelect = (i: number) => this.page.locator(this.fgOptionSelector).nth(i);
    private newRoleSelect = this.page.locator('.grid >div>div>.peer:not([role="switch"])').first();
    //private newRoleNameSelect = this.page.locator('.grid >div>div>div>label:First-Child').filter({ hasText: 'Create Role' });
    private facOptionSelector: string = 'div[data-slot="command-group"] div[data-slot="command-item"]';
    private facilitySelect = (name: string) => this.page.locator('div[data-slot="command-group"] div[data-slot="command-item"]:has-text("' + name + '")');
    private untilOptions = (i: number) => this.page.locator('div[data-slot="command-group"] div[data-slot="command-item"]').nth(i);


    /**
     * Navigates to the default page and logs in if required.
     */
    async navigateToDefaultPage() {
        await this.base.goto(process.env.TESTURL);
        await setTimeout(5000);
        if (this.page.url() !== process.env.TESTURL) {
            await this.base.commonLogin();
        }
    }

    /**
      * Login to Portal with Specific User
      */
    async specificUserLogin(user: CreatedUser) {
        await fixture.page.goto(process.env.TESTURL, {
            waitUntil: "domcontentloaded"
        });
        await setTimeout(5000);
        await this.page.waitForSelector("#username", { state: "attached" })
        await expect(this.page.getByRole('heading', { name: 'Welcome Back' })).toBeVisible();
        fixture.logger.info(`loginuser name : ${user.username}`);
        if (await this.page.locator("div.login-container").isVisible()) {
            //await clickElement(this.page.locator(".login-text"));
            await fillElement(this.page.locator("#username"), user.username);
            await fillElement(this.page.locator("#password"), user.password);
            await setTimeout(1000);
            await clickElement(this.page.locator("#btn-login"));
            await this.page.waitForLoadState("domcontentloaded");
            const userp = fixture.page.locator(".bg-grey");
            await expect(userp).toBeVisible({ timeout: 30000 });
            //await fixture.page.context().storageState({ path: "src/helper/auth/admin.json" });
            //await this.base.goto(process.env.TESTURL);
        }

    }
    /**
      * log out from the portal
      */
    async logOutFromPortal() {
        await expect(this.page.getByRole('button', { name: 'User profile menu' })).toBeVisible();
        await this.page.getByRole('button', { name: 'User profile menu' }).click();
        await this.page.getByText('Logout').click();
        await setTimeout(1000);
        await this.page.waitForLoadState("domcontentloaded");
    }

    /**
     * Fills user details in the registration form.
     */
    async addUserDetails(user: User, company: string, functionalArea: string) {
        await clickElement(this.elements.firstNameInput);
        await fillElement(this.elements.firstNameInput, user.firstName);
        await clickElement(this.elements.lastNameInput);
        await fillElement(this.elements.lastNameInput, user.lastName);
        await clickElement(this.elements.companyDropdown);
        await clickElement(this.companyOption(company));
        await clickElement(this.elements.positionInput);
        await fillElement(this.elements.positionInput, user.position);
        await waitAndClickElement(this.elements.principalFacilityGroupDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupSelect(1));
        await waitAndClickElement(this.elements.principalFacilityDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupSelect(0));
        if (company !== 'QUASEP') {
            await clickElement(this.elements.functionalAreaDropdown);
            for (const area of functionalArea.split(", ")) {
                await fixture.page.waitForSelector(this.facOptionSelector);
                await forceClickElement(this.facilitySelect(area));
            }
            await this.base.escapeOnDropdown();
        }
        await clickElement(this.elements.usernameInput);
        await fillElement(this.elements.usernameInput, user.username);
        await clickElement(this.elements.passwordInput);
        await fillElement(this.elements.passwordInput, user.password);
    }

    /**
     * Selects a role for the user and fills additional fields if required.
     */
    async addRoles(role: string, newRole?: string) {
        await clickElement(this.elements.roleSelectionButton);
        await clickElement(this.roleSelect(role));
        if (role === 'Account Manager' || role === 'Regional Director' || role === 'Client Group Admin') {
            await setTimeout(1000);
            if (role !== 'Regional Director') {
                await clickElement(role === "Client Group Admin" ? this.elements.accountManagerButtonAlt : this.elements.accountManagerButton);
                await forceClickElement(this.regMan);
            }
            await clickElement(this.elements.primaryProvinceButton);
            await setTimeout(500);
            await forceClickElement(this.regMan);
        } else if (role === 'Education Coordinator') {
            await setTimeout(1000);
            await clickElement(this.elements.instructorPortalDepartmentButton);
            await clickElement(this.untilOptions(2));
            await this.base.escapeOnDropdown();
            await clickElement(this.elements.instructorPortalFacilityButton);
            await clickElement(this.untilOptions(2));
            await this.base.escapeOnDropdown();
            //await clickElement(this.elements.instructorPortalTypeButton);
            await clickElement(this.elements.cmfaCheckbox);
        }

        if (newRole) {

            await clickElement(this.newRoleNameOpt(newRole));
        } else {

            await clickElement(this.newRoleSelect);
        }
    }

    /**
     * Fills contact details for the user including phone and address.
     */
    async addContactDetails(user: User, phoneType: string, addressType: string) {
        await clickElement(this.elements.emailInput);
        await fillElement(this.elements.emailInput, user.email);
        await clickElement(this.elements.addNewPhoneButton);
        await clickElement(this.elements.phoneNumberInput);

        if (phoneType === 'Cell/Mobile' && user.phoneno.length > 10) {
            user.phoneno = user.phoneno.substring(0, 10);
        }
        await fillElement(this.elements.phoneNumberInput, user.phoneno);
        if (phoneType !== "Main") {
            await clickElement(this.elements.phoneTypeButton);
            await clickElement(this.addPhoneType(phoneType));
        }
        await clickElement(this.elements.addNewAddressButton);
        await clickElement(this.elements.addressLine1Input);
        await fillElement(this.elements.addressLine1Input, user.address.addressLine1);
        await clickElement(this.elements.addressLine2Input);
        await fillElement(this.elements.addressLine2Input, user.address.addressLine2);
        await clickElement(this.elements.cityInput);
        await fillElement(this.elements.cityInput, user.address.city);
        await clickElement(this.elements.postalCodeInput);
        await fillElement(this.elements.postalCodeInput, user.address.postcode);
        await clickElement(this.elements.stateProvinceButton);
        await clickElement(this.regMan);
        if (addressType !== "Main") {
            const count = await this.elements.addressTypeButton.count();
            await clickElement(this.elements.addressTypeButton.nth(count - 1));
            await clickElement(this.addPhoneType(addressType));
        }
    }

    /**
     * Adds third-party application details for the user.
     */
    async addThirdParty(thirdParty: string, filteredApps: string[]) {
        await clickElement(this.elements.addThirdPartyButton);
        await clickElement(this.elements.thirdPartyOptionButton);
        await clickElement(this.thirdPartySelect(thirdParty));
        if (!filteredApps.includes(thirdParty)) {
            await clickElement(this.elements.thirdPartyUsernameInputAlt);
            await fillElement(this.elements.thirdPartyUsernameInputAlt, "ddd");
        }
    }

    /**
     * Verifies user details on the UI after registration.
     */
    async verifyUser(user: User, extraSkipParams: string[] = []) {
        const skipParams = ['password', 'phoneno', ...extraSkipParams];
        for (const key in user) {
            if (skipParams.includes(key)) continue;
            if (typeof user[key] !== 'object') {
                fixture.logger.info(`user key value : ${user[key]}`);
                if (user[key] === 'CPS') {
                    await expect(this.verifyExactText(user[key])).toContainText(user[key], { timeout: 5000 });
                } else {
                    await expect(this.verifyText(user[key])).toContainText(user[key], { timeout: 5000 });
                }
            } else {
                for (const subKey in user[key]) {
                    if (user[key].hasOwnProperty(subKey)) {
                        await expect(this.verifyText(user[key][subKey])).toContainText(user[key][subKey], { timeout: 5000 });
                    }
                }
            }
        }
    }

    /**
     * Navigates to the Add New User form.
     */
    async navigateToAddNewUserForm() {
        await clickElement(this.elements.addNewUser);
    }

    /**
     * Registers a new user with all required details and verifies creation.
     */
    async registerUser({ company, role, phoneType, addressType, thirdParty, functionalArea, verifyFromEdit = false, newRole }: { company: string; role: string; phoneType: string; addressType: string; thirdParty: string; functionalArea: string; verifyFromEdit?: boolean; newRole?: string; }): Promise<void> {
        const user: User = generateUser();
        await fixture.logger.info(JSON.stringify(user));
        if (!verifyFromEdit) {
            await this.elements.addNewUser.evaluate((node) => node.click());
        }

        // Fetch applications if not already cached
        if (fixture.commonContext?.users?.length === 0 && (!fixture.commonContext.filteredApps || fixture.commonContext.filteredApps.length === 0)) {
            try {
                fixture.logger.info("Attempting to fetch applications...");
                await this.page.reload();
                const app = await this.base.waitForResponse("applications");
                if (fixture.commonContext) {
                    fixture.commonContext.filteredApps = app.filter((res: any) => res.defaultToPortalUsername).map((res: any) => res.name);
                    fixture.logger.info(`Fetched ${fixture.commonContext.filteredApps.length} filtered applications`);
                }
            } catch (error: any) {
                fixture.logger.error(`Failed to fetch applications: ${error.message}`);
                // Fallback: Use default applications or skip third-party selection
                if (fixture.commonContext) {
                    fixture.commonContext.filteredApps = ["Purchasing", "PartsTown", "HUBERT", "Analytics Dashboard"];
                    fixture.logger.info("Using fallback applications list");
                }
            }
        }
        await this.verifyCreateUserValidations();
        await this.addUserDetails(user, company, functionalArea);
        if (newRole) {

            await this.addRoles(role, newRole);
        } else {

            await this.addRoles(role);
        }
        await this.addContactDetails(user, phoneType, addressType);
        if (company !== 'QUASEP') {
            await this.addThirdParty(thirdParty, fixture.commonContext.filteredApps || []);
        }
        await fixture.logger.info(JSON.stringify(user));
        await clickElement(this.elements.submitButton);
        await this.base.verifyToast("User created successfully");
        user.role = role;
        user.addressType = addressType;
        user.phoneType = phoneType;
        user.thirdparty = thirdParty;
        user.company = company;
        user.functionalArea = functionalArea.split(", ").sort().join(", ");
        fixture.commonContext.users.push({ username: user.username, password: user.password, lastName: user.lastName, firstName: user.firstName });
        await this.verifyUser(user, company === 'QUASEP' ? ['thirdparty', 'functionalArea'] : []);
        if (!verifyFromEdit) {
            await clickElement(this.elements.userHomeLink);
        }
    }

    /**
     * Creates a new user and verifies creation.
     */
    async createNewUser(newRoleName?: string) {
        await this.elements.addNewUser.click();
        await fillElement(this.elements.firstNameInput, "testuser");
        await this.base.verifyResetPopup();
        await this.base.verifyunsavedChangePopup();
        await this.elements.firstNameInput.clear();
        await this.registerUser({
            company: 'CPS',
            role: 'Account Manager',
            phoneType: 'Work',
            addressType: 'Main',
            thirdParty: 'Purchasing',
            functionalArea: 'Activation, Nursing, Environmental, Maintenance',
            verifyFromEdit: true,
            newRole: newRoleName
            //newRole: "National Implementation Agent"
        });
    }
    async createNewUserAndSearch(newrole: Roles) {

        await clickElement(this.elements.userHomeLink);
        await this.createNewUser(newrole.rolename);
        await clickElement(this.elements.userHomeLink);
        await this.searchForNewUser();
    }


    /**
     * Edits a newly created user.
     */
    async editNewUser() {
        await clickElement(this.elements.userEditButton);
        await this.editUser({
            company: 'CPS',
            role: 'Account Manager',
            phoneType: 'Work',
            addressType: 'Main',
            thirdParty: 'Purchasing',
            functionalArea: 'Activation, Nursing, Environmental, Maintenance',
            verifyFromView: true
        });
    }

    async EditUserNewRole(newrole: Roles) {
        await clickElement(this.elements.userGridEditButton);
        await clickElement(this.newRoleNameOpt(newrole.rolename));
        await setTimeout(2000);
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("User updated successfully");
        //Admin Level -1
        //External Supplier

    }

    /**
     * Inactivates a user and verifies confirmation dialog.
     */
    async inactivateUser() {
        //await clickElement(this.elements.userGridEditButton);
        await clickElement(this.elements.userEditButton);
        await clickElement(this.elements.statusSwitch);
        await clickElement(this.elements.saveButton);
        await expect(this.page.getByRole('paragraph')).toContainText('The email address and mobile number will be removed. Do you wish to continue?');
        await expect(this.page.getByLabel('Confirmation').locator('span')).toContainText('Confirmation');
        await expect(this.page.getByRole('button', { name: 'Yes, Continue' })).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'No, Cancel' })).toBeVisible();
        await expect(this.page.getByRole('button', { name: 'Close dialog' })).toBeVisible();
        await clickElement(this.elements.confirmInactiveButton);
        await this.base.verifyToast("User updated successfully");
        await expect(this.page.getByRole('main')).toContainText('-');
    }

    /**
     * Navigates to the User Home page.
     */
    async navigateToUserHome() {
        await clickElement(this.elements.userHomeLink);
    }

    /**
     * Searches for the newly created user using username.
     */
    async searchForNewUser(): Promise<void> {

        const latestOrFirstUser = fixture.commonContext.getLatestUser();
        console.log(fixture.commonContext.getLatestUser());
        const firstUsername = latestOrFirstUser.username;
        fixture.logger.info(`user from fixture : ${firstUsername}`);
        await fillElement(this.elements.userNameInput, firstUsername);
        //await fillElement(this.elements.userNameInput, 'Ilene51');
        await clickElement(this.elements.usersearchButton);
        await setTimeout(500);
    }

    /**
     * Fills user details in the edit user form.
     */
    async editUserDetails(user: User, company: string, functionalArea: string) {
        await clickElement(this.elements.firstNameInput);
        await fillElement(this.elements.firstNameInput, user.firstName);
        await clickElement(this.elements.lastNameInput);
        await fillElement(this.elements.lastNameInput, user.lastName);
        await clickElement(this.elements.editCompanyDropdown);
        await clickElement(this.companyOption(company));
        await clickElement(this.elements.positionInput);
        await fillElement(this.elements.positionInput, user.position);
        await waitAndClickElement(this.elements.principalFacilityGroupDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupSelect(1));
        await waitAndClickElement(this.elements.principalFacilityDropdown, 500);
        await fixture.page.waitForSelector(this.fgOptionSelector);
        await forceClickElement(this.facilityGroupSelect(1));
        await clickElement(this.elements.functionalAreaDropdown);
        await clickElement(this.elements.clearFunctionalAreaButton);
        await setTimeout(500);
        for (const area of functionalArea.split(", ")) {
            await fixture.page.waitForSelector(this.facOptionSelector);
            await forceClickElement(this.facilitySelect(area));
            await setTimeout(100);
        }
        await this.base.escapeOnDropdown();
    }

    /**
     * Selects a role for the user in edit mode and fills additional fields if required.
     */
    async editRoles(role: string) {
        await clickElement(this.elements.editRoleSelectionButton);
        await clickElement(this.roleSelect(role));
        if (role === 'Account Manager' || role === 'Regional Director' || role === 'Client Group Admin') {
            await setTimeout(500);
            if (role !== 'Regional Director') {
                await clickElement(role === "Client Group Admin" ? this.elements.accountManagerButtonAlt : this.elements.accountManagerButton);
                await forceClickElement(this.regMan);
            }
            await clickElement(this.elements.primaryProvinceButton);
            await setTimeout(500);
            await forceClickElement(this.regMan);
        } else if (role === 'Education Coordinator') {
            await setTimeout(1000);
            await clickElement(this.elements.instructorPortalDepartmentButton);
            await clickElement(this.elements.instructorPortalTypeButton);
            await clickElement(this.elements.cmfaCheckbox);
        }
        await clickElement(this.newRoleSelect);
    }

    /**
     * Fills contact details for the user in edit mode.
     */
    async editContactDetails(user: User, phoneType: string, addressType: string) {
        await clickElement(this.elements.emailInput);
        await fillElement(this.elements.emailInput, user.email);

        const contactCount = await this.elements.deleteContactButton.count();
        if (contactCount > 0) {
            for (let i = 0; i < contactCount; i++) {
                await this.elements.deleteContactButton.nth(0).click();
                await setTimeout(1000);
            }
        }
        await clickElement(this.elements.addNewPhoneButton);
        await clickElement(this.elements.phoneNumberInput);

        if (phoneType === 'Cell/Mobile' && user.phoneno.length > 10) {
            user.phoneno = user.phoneno.substring(0, 10);
        }
        await fillElement(this.elements.phoneNumberInput, user.phoneno);
        if (phoneType !== "Main") {
            await clickElement(this.elements.phoneTypeButton);
            await clickElement(this.addPhoneType(phoneType));
        }
        await clickElement(this.elements.addNewAddressButton);
        await clickElement(this.elements.addressLine1Input);
        await fillElement(this.elements.addressLine1Input, user.address.addressLine1);
        await clickElement(this.elements.addressLine2Input);
        await fillElement(this.elements.addressLine2Input, user.address.addressLine2);
        await clickElement(this.elements.cityInput);
        await fillElement(this.elements.cityInput, user.address.city);
        await clickElement(this.elements.postalCodeInput);
        await fillElement(this.elements.postalCodeInput, user.address.postcode);
        await clickElement(this.elements.stateProvinceButton);
        await clickElement(this.regMan);
        if (addressType !== "Main") {
            const count = await this.elements.addressTypeButton.count();
            await clickElement(this.elements.addressTypeButton.nth(count - 1));
            await clickElement(this.addPhoneType(addressType));
        }
    }

    /**
     * Edits third-party application details for the user.
     */
    async editThirdParty(thirdParty: string, filteredApps: string[]) {
        const thirdPartyCount = await this.elements.deleteThirdPartyButton.count();
        if (thirdPartyCount > 0) {
            for (let i = 0; i < thirdPartyCount; i++) {
                await this.elements.deleteThirdPartyButton.nth(0).click();
                await setTimeout(1000);
            }
        }
        await clickElement(this.elements.addThirdPartyButton);
        await clickElement(this.elements.editThirdPartyOptionButton);
        await clickElement(this.thirdPartySelect(thirdParty));
        if (!filteredApps.includes(thirdParty)) {
            await clickElement(this.elements.editThirdPartyUsernameInput);
            await fillElement(this.elements.editThirdPartyUsernameInput, "ddd");
        }
    }

    /**
     * Verifies user details on the UI after editing.
     */
    async verifyEditUser(user: User, extraSkipParams: string[] = []) {
        const skipParams = ['password', 'username', 'phoneno', ...extraSkipParams];
        for (const key in user) {
            if (skipParams.includes(key)) continue;
            if (typeof user[key] !== 'object') {
                fixture.logger.info(`user key value : ${user[key]}`);
                if (user[key] === 'CPS') {
                    await expect(this.verifyExactText(user[key])).toContainText(user[key], { timeout: 5000 });
                } else {
                    await expect(this.verifyText(user[key])).toContainText(user[key], { timeout: 5000 });
                }
            }
            else {
                for (const subKey in user[key]) {
                    if (user[key].hasOwnProperty(subKey)) {
                        fixture.logger.info(`user key value : ${user[key][subKey]}`);
                        await expect(this.verifyText(user[key][subKey])).toContainText(user[key][subKey], { timeout: 5000 });
                    }
                }
            }
        }

        await this.base.verifyUserCreateorModifydate(true);
        //fixture.logger.info("View user page verified successfully after user updation");
    }

    /**
     * Verifies external user details after editing.
     */
    async verifyExtEditUser(user: User | ExtUser) {
        const keysToVerify: (keyof User)[] = ["role"];
        for (const key of keysToVerify) {
            const value = user[key];
            if (!value || value.toString().trim() === "") continue;
            fixture.logger.info(`user key value : ${value}`);
            await expect(this.verifyText(value)).toContainText(value, { timeout: 5000 });
        }
    }

    /**
     * Verifies that the correct number of disabled elements are present after editing a user.
     */
    async verifyEditUserDisabledElements() {
        const count = await this.elements.disabledElements.count();
        expect([4, 5, 6]).toContain(count);
    }

    /**
     * Verifies that the correct number of disabled elements are present after editing an external user.
     */
    async verifyExtEditUserDisabledElements() {
        // const disabledFieldsCount = await this.elements.disabledElements.count();
        // await assert.assertToBeGreaterThan(9, disabledFieldsCount);
        await this.base.verifyDisabledElementsCount(7, "User details", 'greaterThan');
    }

    /**
     * Edits a user with new details and verifies the update.
     */
    async editUser({ company, role, phoneType, addressType, thirdParty, functionalArea, verifyFromView = false }: { company: string; role: string; phoneType: string; addressType: string; thirdParty: string; functionalArea: string; verifyFromView?: boolean; }) {
        const editUser: User = generateUser();
        if (!verifyFromView) {
            await clickElement(this.elements.userGridEditButton);
        }
        await this.editUserDetails(editUser, company, functionalArea);
        await this.verifyEditUserDisabledElements();
        await this.editRoles(role);
        await this.editContactDetails(editUser, phoneType, addressType);
        if (company !== 'QUASEP') {
            await this.editThirdParty(thirdParty, fixture.commonContext.filteredApps || []);
        }
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("User updated successfully");
        editUser.role = role;
        editUser.addressType = addressType;
        editUser.phoneType = phoneType;
        editUser.thirdparty = thirdParty;
        editUser.company = company;
        editUser.functionalArea = functionalArea.split(", ").sort().join(", ");
        fixture.commonContext.users.push({ username: editUser.username, password: editUser.password });
        await fixture.logger.info(JSON.stringify(editUser));
        await this.verifyEditUser(editUser, company === 'QUASEP' ? ['thirdParty'] : []);
        await clickElement(this.elements.userHomeLink);
    }

    /**
     * Edits an external user and verifies the update.
     */
    async editExtUser({ role }: { role: string; }) {
        const editUser: User = generateUser();
        await this.verifyExtEditUserDisabledElements();
        await this.editRoles(role);
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("User updated successfully");
        editUser.role = role;
        await this.verifyExtEditUser(editUser);
        await clickElement(this.elements.userHomeLink);
    }

    /**
     * Views user details and verifies expected UI elements.
     */
    async viewUser(options?: { verifyFromView?: boolean }) {
        if (!options?.verifyFromView) {
            await clickElement(this.elements.userGridViewButton);
            await this.base.waitForResponse("detail");
        }
        await expect(this.page.getByRole('heading')).toContainText('User Info');
        //"Functional Area",
        const expectedTexts = [
            "User Details",
            "First Name",
            "User Roles",
            "New Roles",
            "Contact Details",
            "Phone Numbers",
            "Addresses",
            "Admin Notes",
            "Notes",
            "Last Name",
            "Language",
            "Position",
            "Brand",
            "Principal Facility Group",
            "Principal Facility",
            "Username",
            "Account Manager",
            "Status",
            "My CPS Account #",
            "Created By",
            "Date Created",
            "Last Modified By",
            "Date Last Modified",
            "Legacy Role"
        ];
        for (const text of expectedTexts) {
            await assert.assertElementContainsText(this.verifyExactText(text), text);
        }


        // For View Page
        await this.base.verifyUserCreateorModifydate(false);
        fixture.logger.info("View user page verified successfully");

        const expectedButtons = [
            "Go back",
            "Edit",
            "User Details",
            "User Roles",
            "Contact Details",
            "Admin Notes"
        ];
        for (const btn of expectedButtons) {
            await assert.assertElementIsVisible(this.verifyButton(btn));
        }
    }

    //create user form validations

    async verifyCreateUserValidations() {

        await this.verifyPageUI({
            headingText: "Add New User",

            // Static texts that should exist inside <main>
            requiredMainTexts: [
                "User Details",
                "First Name",
                "Last Name",
                "Brand",
                "Language",
                "Position",
                "Principal Facility Group",
                "Principal Facility",
                "Functional Area",
                "Username",
                "Password",
                "Status",
                "User Roles",
                "Legacy Role",
                "New Roles",
                "Contact Details",
                "Email",
                "Phone Numbers",
                "Addresses",
                "3rd Party Authorization Credentials",
                "Admin Notes",
                "Notes",

            ],

            // Buttons + specific elements
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
                { locator: this.page.getByRole('button', { name: 'Submit' }), label: "Button 'Submit'" },
                { locator: this.page.getByRole('button', { name: 'User Details' }), label: "Button 'User Details'" },
                { locator: this.page.getByRole('button', { name: 'User Roles' }), label: "Button 'User Roles'" },
                { locator: this.page.getByRole('button', { name: 'Contact Details' }), label: "Button 'Contact Details'" },
                { locator: this.page.getByRole('button', { name: '3rd Party Authorization Credentials' }), label: "Button '3rd Party Authorization Credentials'" },
                { locator: this.page.getByRole('button', { name: 'Admin Notes' }), label: "Button 'Admin Notes'" },
                { locator: this.page.locator('div').filter({ hasText: /^Active$/ }).getByRole('switch'), label: "Active switch" },
            ]
        });


        await this.base.verifyDisabledElementsCount(2, "Create User page", 'equal', this.page.locator('div[data-state="open"] button[data-slot="popover-trigger"]:disabled'));
        await assert.assertElementContainsText(this.verifyExactText('Please provide at least one contact method — Email or Phone Number.'), 'Please provide at least one contact method — Email or Phone Number.');
        await this.page.getByRole('button', { name: 'Submit' }).click();
        // await expect(this.page.getByRole('alert')).toContainText(
        //     'Please enter valid data before submitting the form.'
        // );
        await this.base.verifyToast('Please enter valid data before submitting the form.')

        // Define all required error texts
        const requiredMainErrorTexts: string[] = [
            'First name is required',
            'Last name is required',
            'Brand is required',
            'Language is required',
            'Facility group is required',
            'Facility is required',
            'Functional area is required',
            'Username is required',
            'Password is required',
            'Role is required',
            'Email or phone number is required',
        ];

        // Loop through the texts and assert visibility
        for (const text of requiredMainErrorTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            fixture.logger.info(`Main text "${text}" is visible`);
        }

        //todo

        await fillElement(this.elements.firstNameInput, 'jdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfkjdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfk');
        await fillElement(this.elements.lastNameInput, 'jdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfkjdkfhsldfhkdskfjksdhkfhsdkfsldjsnfkndfsdfk');
        await assert.assertElementContainsText(this.verifyExactText('First name must not exceed 50 characters'), 'First name must not exceed 50 characters');
        await assert.assertElementContainsText(this.verifyExactText('Last name must not exceed 50 characters'), 'Last name must not exceed 50 characters');
        //First name must not exceed 50 characters
        await fillElement(this.elements.userNameInput, 'testuser');
        await assert.assertElementContainsText(this.verifyExactText('This username is already in use. Please enter another.'), 'This username is already in use. Please enter another.');
        await fillElement(this.elements.passwordInput, 'tr');
        await assert.assertElementContainsText(this.verifyExactText('Password must be at least 12 characters'), 'Password must be at least 12 characters');
        await fillElement(this.elements.passwordInput, '*************');
        await assert.assertElementContainsText(this.verifyExactText('Password must contain at least one uppercase letter'), 'Password must contain at least one uppercase letter');


        await clickElement(this.elements.roleSelectionButton1);
        await clickElement(this.roleSelect('Account Manager'));
        await clickElement(this.elements.submitButton);
        await assert.assertElementContainsText(this.verifyExactText('Vice president of operations is required'), 'Vice president of operations is required');
        await assert.assertElementContainsText(this.verifyExactText('Primary province is required'), 'Primary province is required');

        await clickElement(this.elements.roleSelectionButton1);
        await clickElement(this.roleSelect('Regional Director'));
        await clickElement(this.elements.submitButton);
       //todo
       // need to condirm is this defect or not
       //  await assert.assertElementContainsText(this.verifyExactText('Primary province is required'), 'Primary province is required');

        await clickElement(this.elements.roleSelectionButton1);
        await clickElement(this.roleSelect('Client Group Admin'));
        await clickElement(this.elements.submitButton);

        await assert.assertElementContainsText(this.verifyExactText('Account manager is required'), 'Account manager is required');
        await assert.assertElementContainsText(this.verifyExactText('Primary province is required'), 'Primary province is required');

        await clickElement(this.elements.roleSelectionButton1);
        await clickElement(this.roleSelect('Education Coordinator'));
        await clickElement(this.elements.submitButton);
        await assert.assertElementContainsText(this.verifyExactText('At least one department must be selected'), 'At least one department must be selected');
        await assert.assertElementContainsText(this.verifyExactText('At least one facility must be selected'), 'At least one facility must be selected');

        await clickElement(this.elements.addNewPhoneButton);
        await clickElement(this.elements.submitButton);
        await assert.assertElementContainsText(this.verifyExactText('Phone number is required'), 'Phone number is required');


        await clickElement(this.elements.addNewAddressButton);
        await clickElement(this.elements.submitButton);
        await assert.assertElementContainsText(this.verifyExactText('Address line 1 is required'), 'Address line 1 is required');
        await assert.assertElementContainsText(this.verifyExactText('City is required'), 'City is required');
        await assert.assertElementContainsText(this.verifyExactText('Postal code is required'), 'Postal code is required');
        await assert.assertElementContainsText(this.verifyExactText('Province is required'), 'Province is required');
        //validate phone number types
        await this.base.validateDropdownOptions(
            this.page.getByRole('row', { name: 'Main United States/Canada (+1' }).getByLabel('Main'),
            ["Main", "Work", "Cell/Mobile", "Fax", "Home", "Pager"],
            true
        );
        await this.base.escapeOnDropdown();

        await this.base.validateDropdownOptions(
            this.page.getByRole('button', { name: 'Canada', exact: true }),
            ["Canada", "USA"],
            true
        );

        await this.base.escapeOnDropdown();

        //validate proviance basedon country canada drop down options and order
        await this.base.validateDropdownOptions(
            this.page.getByRole('cell', { name: 'Select one Province is' }).getByLabel('Select one'),
            ["AB", "BC", "MB", "NB", "NL", "NS", "NT", "NU", "ON", "PE", "QC", "SK", "YT"]

        );
        await this.base.escapeOnDropdown();

        await this.page.getByRole('button', { name: 'Canada', exact: true }).click();
        await this.page.getByRole('menuitem', { name: 'USA' }).click();
        await setTimeout(300);

        //validate proviounce based on country selection options and order
        await this.base.validateDropdownOptions(
            this.page.getByRole('cell', { name: 'Select one Province is' }).getByLabel('Select one'),
            ["PA", "FL", "GA", "HI", "IA", "ID", "IL", "IN", "KS", "KY", "LA", "MA", "MD", "ME", "MI", "MN", "MO", "MS", "MT", "NE", "NC", "ND", "NH", "NJ", "NM", "NY", "NV", "OH", "OK", "RI", "SC", "SD", "TN", "TX", "UT", "VA", "VT", "WA", "WI", "OR", "WV", "WY", "AK", "AL", "AR", "AZ", "CA", "CO", "CT", "DE"]

        );

        await this.base.escapeOnDropdown();
        await clickElement(this.elements.addThirdPartyButton);
        await this.base.validateDropdownOptions(this.page.locator('td[class$=text-xs] button[data-slot="dropdown-menu-trigger"]'), ["Purchasing", "Dashboard", "HUBERT", "PartsTown"])
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.submitButton);
        await assert.assertElementContainsText(this.verifyExactText('Application is required'), 'Application is required');
        await assert.assertElementContainsText(this.verifyExactText('Username is required'), 'Username is required');
        // clear all errors
        await this.page.getByRole('button', { name: 'Reset Changes' }).click();
        await this.page.getByRole('button', { name: 'Yes, Proceed' }).click();



    }

    //common verication page defaults

    async verifyPageUI({
        headingText,
        requiredMainTexts = [],
        commonElements = [],
        pageSpecificElements = []
    }: {
        headingText: string;
        requiredMainTexts?: string[];
        commonElements?: { locator: any; label: string }[];
        pageSpecificElements?: { locator: any; label: string }[];
    }) {
        // Verify heading text
        await expect(this.page.getByRole('heading')).toContainText(headingText);
        //fixture.logger.info(` Heading "${headingText}" is visible`);

        //  Verify additional static texts inside main
        for (const text of requiredMainTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            //fixture.logger.info(` Main text "${text}" is visible`);
        }

        //  Define default common elements (can be overridden if passed in)
        const defaultCommonElements = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
        ];

        //  Merge everything
        const allElements = [...defaultCommonElements, ...commonElements, ...pageSpecificElements];

        //  Verify all UI elements with logging
        for (const { locator, label } of allElements) {
            await this.base.verifyWithLogging(locator, label);
        }
    }

    /**
     * Views and verifies external user details and UI elements.
     */
    async viewExternalUser() {
        await clickElement(this.elements.userGridEditButton);
        await expect(this.page.getByRole('heading', { name: 'Edit User' })).toBeVisible();


        const expectedTexts = [
            "User Details",
            "First Name",
            "User Roles",
            "New Roles",
            "Contact Details",
            "Phone Numbers",
            "Addresses",
            "Admin Notes",
            "Notes",
            "Last Name",
            "Language",
            "Position",
            "Brand",
            "Principal Facility Group",
            "Principal Facility",
            "Username",
            "Account Manager",
            "Status",
            "Created By",
            "Date Created",
            "Last Modified By",
            "Date Last Modified",
            "Legacy Role",
            "Password"
        ];
        for (const text of expectedTexts) {
            await assert.assertElementContainsText(this.verifyExactText(text), text);
        }


        const expectedButtons = [
            "Go back",
            "Reset Changes",
            "Save",
            "User Details",
            "User Roles",
            "Contact Details",
            "Admin Notes"
        ];
        for (const btn of expectedButtons) {
            await assert.assertElementIsVisible(this.verifyButton(btn));
        }

        await this.verifyExtEditUserDisabledElements();
    }
    /*async enterUsername(userName: string) {
        await this.page.type(this.Elements.userName, userName);
        const [response] = await Promise.all([
            this.page.waitForResponse(res => {
                return res.status() == 200
                    &&
                    res.url() == `https://bookcart.azurewebsites.net/api/user/validateUserName/${userName}`
            })
        ]);
        await response.finished();
    }*/
}

