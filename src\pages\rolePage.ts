import { expect, Page } from "@playwright/test";
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { Roles } from "../helper/types/role";
import { generateRole } from "../helper/util/common";
import Assert from "../helper/wrapper/assert";
import { ExcelHelper } from '../helper/util/ExcelHelper';
import * as path from 'path';
import * as fs from 'fs';
import { clickElement, fillElement, forceClickElement, hoveOverElement, waitAndClickElement } from "../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";
let assert: Assert;

/**
 * Page Object Model for Role Management Page.
 * Provides methods to interact with and verify UI elements, perform CRUD operations on roles,
 * assign and unassign users, manage permissions, filter and search roles, and validate grid data.
 */
export default class RolePage {

    private base: PlaywrightWrapper;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        assert = new Assert(page);
    }

    // Locators for Role Page elements
    private elements = {
        rolesLink: this.page.getByRole('link', { name: 'Roles' }),
        rolesHeading: this.page.getByRole('heading', { name: 'Roles' }),
        createRoleButton: this.page.getByLabel('Create New Role'),
        createRoleBtn: this.page.getByRole('button', { name: 'Create New Role' }),
        paginationCurrentBtn: this.page.getByRole('combobox', { name: 'Select page size. Current' }),
        paginationIconBtn: this.page.getByRole('navigation', { name: 'Pagination' }).locator('div').nth(3),
        roleNameInput: this.page.getByRole('textbox', { name: 'Role Name' }),
        roleDescriptionInput: this.page.getByRole('textbox', { name: 'Description' }),
        saveAndAddButton: this.page.getByRole('button', { name: 'Save & Add' }),
        saveButton: this.page.getByRole('button', { name: 'Save' }),
        editButton: this.page.getByRole('button', { name: 'Edit' }),
        cancelButton: this.page.getByRole('button', { name: 'Cancel' }),
        resetButton: this.page.getByRole('button', { name: 'Reset' }),
        filterByButton: this.page.getByRole('button', { name: 'Filter by' }),
        filterBySearchButton: this.page.getByRole('button', { name: 'Search' }),
        filterByNameInput: this.page.getByRole('textbox', { name: 'Name', exact: true }),
        filterByUsernameInput: this.page.getByRole('textbox', { name: 'Username' }),
        confirmDeleteButton: this.page.getByRole('button', { name: 'Yes, Delete' }),
        supplierModuleToggle: this.page.getByRole('button', { name: 'Supplier toggle Supplier' }),
        roleManagementModuleToggle: this.page.getByRole('button', { name: 'Role Management toggle Role' }),
        userManagementModuleToggle: this.page.getByRole('button', { name: 'User Management toggle User' }),
        facilityManagementModuleToggle: this.page.getByRole('button', { name: 'Facility Management toggle' }),
        searchPermissionsInput: this.page.getByRole('textbox', { name: 'Search permissions' }),
        searchRoleInput: this.page.getByRole('textbox', { name: 'Search' }),
        searchRole: this.page.locator('input[aria-label="Search"]'),
        rolesHomeLink: this.page.getByRole('link', { name: 'Roles' }),
        userHomeLink: this.page.getByRole('link', { name: 'Users' }),
        assignUserButton: this.page.getByRole('button', { name: 'Plus Assign' }),
        exportButton: this.page.getByRole('button', { name: 'Export' }),
        confirmAssignButton: this.page.getByRole('button', { name: 'Assign', exact: true }),
        unassignUserButton: this.page.getByRole('button', { name: 'Minus Unassign' }),
        searchUserButton: this.page.getByRole('button', { name: 'Search' }),
        userCheckbox: this.page.getByLabel('Select row').first(),
        selectAllCheckbox: this.page.getByRole('checkbox', { name: 'Select all' }),
        closeDialogButton: this.page.getByRole('button', { name: 'Close dialog' }),
        confirmUnassignButton: this.page.getByRole('button', { name: 'Yes, Unassign' }),
        brandDropdown: this.page.getByRole('combobox', { name: 'Brand' }),
        usenameFieldInput: this.page.getByRole('textbox', { name: 'Username' }),
        supplierDropdown: this.page.getByRole('combobox', { name: 'Supplier' }),
        fgDropdown: this.page.locator('div').filter({ hasText: /^Facility GroupSelect one or more$/ }).getByLabel('Select one or more'),
        facilityDropdown: this.page.locator('div').filter({ hasText: /^FacilitySelect one or more$/ }).getByLabel('Select one or more'),
        tableRows: this.page.locator('div[data-slot="table-container"] tbody>tr'),
        gridMessage: this.page.locator('.flex>img+p'),
        roleGridEditBtn: this.page.locator('tbody tr:nth-child(1) td:nth-child(5) button[aria-label="Edit Role"]'),
        roleGridViewBtn: this.page.locator('tbody tr:nth-child(1) td:nth-child(5) button[aria-label="View Role"]'),
        roleGridCopyBtn: this.page.locator('tbody tr:nth-child(1) td:nth-child(5) button[aria-label="Duplicate Role"]'),
        roleGridDeleteBtn: this.page.locator('tbody tr:nth-child(1) td:nth-child(5) button[aria-label="Delete Role"]'),
        getSearchOption: this.page.locator('input[data-slot="command-input"]'),
        getSearchResultone: this.page.locator('.p-3>div:first-of-type')
    };

    // Utility locators and maps
    private newRoleNameOpt = (name: string) => this.page.getByRole('checkbox', { name: `${name} checkbox`, exact: true });
    private getRoleTextLocator = (text: string) => this.page.getByText(text).first();
    private brandOptSelectFlt: string = 'div[data-slot="command-item"]';
    private getDeleteRoleButton = this.page.getByLabel('Delete Role', { exact: true }).first();
    private getPermissionToggle = (permission: string) => this.page.getByRole('switch', { name: permission });
    private getCompanyOption = (index: number) => this.page.locator('div[data-slot="command-list"] div[data-slot="command-item"]').nth(index);
    private getTableCell = (rowIndex: number, colIndex: number) =>
        this.page.locator(`div[data-slot="table-container"] tbody>tr:nth-child(${rowIndex})>td`).nth(colIndex);
    private tableRowSelector: string = 'div[data-slot="table-container"] tbody>tr';
    private getBrandOption = (brand: string) =>
        this.page.locator(`div[data-slot="command-group"] div[data-slot="command-item"]:has-text("${brand}")`);
    private getroleNameOption = (roleName: string) =>
        this.page.locator(`div[class$="stretch"]>div label:first-of-type:has-text("${roleName}")`);

    private moduleToggleMap = {
        "Supplier": () => this.elements.supplierModuleToggle,
        "Role Management": () => this.elements.roleManagementModuleToggle,
        "User Management": () => this.elements.userManagementModuleToggle,
        "Facility Management": () => this.elements.facilityManagementModuleToggle
    };

    /**
     * Navigates to the Roles page.
     */
    async navigateToRolePage(): Promise<void> {
        await clickElement(this.elements.rolesLink);
    }

    /**
     * Fills in role details in the form.
     * @param role - Role details object
     */
    async fillRoleDetails(role: Roles): Promise<void> {
        await fillElement(this.elements.roleNameInput, role.rolename);
        await fillElement(this.elements.roleDescriptionInput, role.roledescription);
    }

    /**
     * Searches for a role by name.
     * @param role - Role details object
     */
    async searchRole(role: Roles): Promise<void> {
        await fillElement(this.elements.searchRoleInput, role.rolename);
    }
    async searchbyRole(rolename: String) {
        await fillElement(this.elements.searchRoleInput, rolename);
        await setTimeout(500);
    }


    async roleButtonFromTable(buttonname: String): Promise<void> {
        if (buttonname === "View") {
            await clickElement(this.elements.roleGridViewBtn);

        }
        if (buttonname === "Edit") {
            await clickElement(this.elements.roleGridEditBtn);

        }
        if (buttonname === "Copy") {
            await clickElement(this.elements.roleGridCopyBtn);

        }
        if (buttonname === "Delete") {
            await clickElement(this.elements.roleGridDeleteBtn);

        }

    }
    /**
     * Searches for a permission by name.
     * @param permission - Permission name
     */

    async searchPermission(permission: string): Promise<void> {
        // First validate "no data" case with a dummy search
        await fillElement(this.elements.searchPermissionsInput, "999");
        await expect(this.elements.gridMessage.first()).toHaveText("No data found");

        // Search actual permission All Permission
        await fillElement(this.elements.searchPermissionsInput, permission);

        if (permission !== "All Permission") {
            const section = this.page
                .locator('.p-3 > div:first-of-type')
                .filter({ hasText: permission });

            await expect(section.locator('div > label')).toBeVisible();
        } else {
            await expect(this.elements.gridMessage.first()).toHaveText("No data found");
            await this.elements.searchPermissionsInput.clear();
        }

        fixture.logger.info(`Search by "${permission}" validated successfully`);
    }


    /**
     * Deletes a role and verifies the deletion toast.
     */
    async deleteRole(): Promise<void> {
        await clickElement(this.getDeleteRoleButton);
        await this.verifyConfirmDeletePopup();
        await clickElement(this.elements.confirmDeleteButton);
        await this.base.verifyToast("Role deleted successfully");
    }

    /**
     * Adds permissions to a role for a specific module.
     * @param permission - Permission name
     * @param module - Module name
     */
    async addRolePermissions(permission: string, module: string): Promise<void> {
        const moduleToggle = this.moduleToggleMap[module]?.();
        if (moduleToggle) {
            await clickElement(moduleToggle);
            await clickElement(this.getPermissionToggle(permission));
        } else {
            fixture.logger.warn(`Unknown module: ${module}`);
        }
    }

    /**
     * Assigns a user to the role.
     */
    async assignUser(): Promise<void> {
        await clickElement(this.elements.assignUserButton);
        await clickElement(this.elements.brandDropdown);
        await forceClickElement(this.getCompanyOption(1));
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.searchUserButton);
        await setTimeout(500);
        await clickElement(this.elements.userCheckbox);
        await clickElement(this.elements.confirmAssignButton);
        await setTimeout(1000);
        await expect(this.elements.userCheckbox).toBeVisible();
    }

    // verify assignActiveUserPopup

    async verifyAssignActiveuser() {
        await clickElement(this.elements.assignUserButton);
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Cancel' }), "Button 'Cancel'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Close Cross' }), "Button 'Close Cross'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Search' }), "Button 'Search'");
        const expectedTexts = [
            'Please apply filters to view user data',
            'Assign Active User(s)',
            'Brand',
            'Name',
            'Username',
            'Facility Group',
            'Facility',
            'Supplier'
        ];

        for (const text of expectedTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
        }


        await this.page.getByRole('combobox', { name: 'Brand' }).click();
        await expect(this.page.getByRole('combobox', { name: 'Brand' })).toBeVisible();
        await this.base.escapeOnDropdown();
        await this.page.locator('div').filter({ hasText: /^Facility GroupSelect one or more$/ }).getByLabel('Select one or more').click();
        await expect(this.page.getByRole('combobox', { name: 'Facility Group' })).toBeVisible();
        await this.base.escapeOnDropdown();
        await this.page.locator('div').filter({ hasText: /^FacilitySelect one or more$/ }).getByLabel('Select one or more').click();
        await expect(this.page.getByRole('combobox', { name: 'Facility' })).toBeVisible();
        await this.base.escapeOnDropdown();
        await this.page.getByRole('combobox', { name: 'Supplier' }).click();
        await expect(this.page.getByRole('combobox', { name: 'Supplier' })).toBeVisible();
        await this.page.getByRole('combobox', { name: 'Supplier' }).click();
        await this.page.getByRole('combobox', { name: 'Supplier' }).fill('dddddd');
        await expect(this.page.getByText('No options found')).toBeVisible();
        await this.base.escapeOnDropdown();
        await expect(this.page.getByText('Assign Active User(s)')).toBeVisible();
        await this.page.getByRole('button', { name: 'Close Cross' }).click();
    }

    /**
     * Unassigns a user from the role.
     */
    async unassignUser(): Promise<void> {
        await setTimeout(500);
        await this.base.waitForSelectorVisible(this.tableRowSelector);
        await forceClickElement(this.elements.selectAllCheckbox);
        await clickElement(this.elements.unassignUserButton);
        await this.verifyUnassignUserPopup();
        await clickElement(this.elements.confirmUnassignButton);
    }

    /**
     * Verifies the unassign user confirmation popup.
     */
    async verifyUnassignUserPopup(): Promise<void> {
        await expect(this.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Are you sure you want to unassign the selected users? Once unassigned users will lose access to the modules and permissions of the role');
        await expect(this.page.getByLabel('Yes, Unassign')).toContainText('Yes, Unassign');
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Yes, Unassign' }), "Button 'Yes, Unassign'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'No, Cancel' }), "Button 'No, Cancel'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Close dialog' }), "Button 'Close dialog'");
    }

    /**
     * Verifies the confirm delete popup.
     */
    async verifyConfirmDeletePopup(): Promise<void> {
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Close dialog' }), "Button 'Close dialog'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'Yes, Delete' }), "Button 'Yes, Delete'");
        await this.base.verifyWithLogging(this.page.getByRole('button', { name: 'No, Cancel' }), "Button 'No, Cancel'");
        await expect(this.page.getByLabel('Confirm Delete').locator('span')).toContainText('Confirm Delete');
        await expect(this.page.locator('[role="alertdialog"] [data-slot="alert-dialog-description"]')).toContainText('Are you sure you want to delete this User role? Once deleted, the data cannot be retrieved.');
    }


    //common method to validate role page 

    async verifyRolePage({
        headingText,
        mainTexts = [],
        pageSpecificElements = []
    }: {
        headingText: string;
        mainTexts?: string[];
        pageSpecificElements?: { locator: any; label: string }[];
    }) {
        // Verify heading
        await expect(this.page.getByRole('heading')).toContainText(headingText);
        fixture.logger.info(`Header text: ${headingText}  is visible`);

        // Verify common "Assigned to"
        await expect(this.page.getByRole('main')).toContainText('Assigned to');

        // Verify additional main texts if provided
        for (const text of mainTexts) {
            await expect(this.page.getByRole('main')).toContainText(text);
            fixture.logger.info(`Main text: ${text}  is visible`);
        }

        // Define common buttons
        const commonButtons = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
            { locator: this.page.getByRole('button', { name: 'Expand All' }), label: "Button 'Expand All'" },
            { locator: this.page.getByRole('button', { name: 'Collapse All' }), label: "Button 'Collapse All'" },
            { locator: this.page.getByRole('button', { name: 'Supplier toggle Supplier' }), label: "Button 'Supplier toggle Supplier'" },
            { locator: this.page.getByRole('button', { name: 'User Management toggle User' }), label: "Button 'User Management toggle User'" },
            { locator: this.page.getByRole('button', { name: 'Role Management toggle Role' }), label: "Button 'Role Management toggle Role'" },
            { locator: this.page.getByRole('button', { name: 'Facility Management toggle' }), label: "Button 'Facility Management toggle'" },
            { locator: this.page.getByRole('textbox', { name: 'Search permissions' }), label: "search input Box 'Search For permissions'" },
        ];

        // Merge page-specific + common
        const allElements = [...pageSpecificElements, ...commonButtons];

        // Verify all elements
        for (const { locator, label } of allElements) {
            await this.base.verifyWithLogging(locator, label);
        }
    }



    /**
     * Verifies the presence and visibility of key UI elements and buttons on the Role View page.
     *
     * This method performs the following checks:
     * - Ensures specific text content is present in the main section and headings.
     * - Confirms the visibility of the 'Search permissions' textbox.
     * - Verifies the presence of the 'Modules & Permissions' text.
     * - Iterates through a predefined list of buttons and toggles, checking their visibility and logging the results.
     *
     * @returns {Promise<void>} A promise that resolves when all verifications are complete.
     */
    async verifyRoleViewPageButtons() {


        await this.verifyRolePage({
            headingText: 'Role Info',
            mainTexts: ['Modules & Permissions'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Export' }), label: "Button 'Export'" },
                { locator: this.page.getByRole('button', { name: 'Filter by' }), label: "Button 'Filter by'" },
                { locator: this.page.getByRole('button', { name: 'Edit' }), label: "Button 'Edit'" },
            ]
        });


    }
    /**
     * Verifies the presence and visibility of key UI elements and buttons on the Create Role page.
     *
     * This method performs the following checks:
     * - Ensures specific text content is present in the main section and headings.
     * - Confirms the visibility of the 'Search permissions' textbox.
     * - Verifies the presence of the 'Modules & Permissions' text.
     * - Iterates through a predefined list of buttons and toggles, checking their visibility and logging the results.
     *
     * @returns {Promise<void>} A promise that resolves when all verifications are complete.
     */
    async verifyCreateRolePageButtons() {

        await this.verifyRolePage({
            headingText: 'Add New Role',
            mainTexts: ['Modules & Permissions', 'No Users Assigned'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
                { locator: this.page.getByRole('button', { name: 'Save & Add' }), label: "Button 'Save & Add'" },
            ]
        });


    }

    async verifyCopyRolePageButtons() {
        //await expect(this.page.getByRole('textbox', { name: 'Role Name' })).toHaveValue(/.* - Copy$/);
        await expect(this.page.getByRole('textbox', { name: 'Role Name' })).toHaveValue(/.*(?: - Copy)+$/);;
        //await expect(this.page.getByRole('textbox', { name: 'Description' })).toHaveValue(/.* - Copy$/);
        await expect(this.page.getByRole('textbox', { name: 'Description' })).toHaveValue(/.*(?: - Copy)+$/);;
        await expect(this.page.getByRole('main')).toContainText('Assigned to');
        await expect(this.page.getByRole('main')).toContainText('No Users Assigned');
        await expect(this.page.getByRole('main')).toContainText('Modules & Permissions');

        const elementsToVerify = [
            { locator: this.page.getByRole('button', { name: 'Go back' }), label: "Button 'Go back'" },
            { locator: this.page.getByRole('heading', { name: 'Copy Role' }), label: "Heading 'Copy Role'" },
            { locator: this.page.getByRole('button', { name: 'Reset' }), label: "Button 'Reset'" },
            { locator: this.page.getByRole('button', { name: 'Save & Add' }), label: "Button 'Save & Add'" },
            { locator: this.page.getByRole('textbox', { name: 'Search permissions' }), label: "Textbox 'Search permissions'" },
            { locator: this.page.getByRole('button', { name: 'Expand All' }), label: "Button 'Expand All'" },
            { locator: this.page.getByRole('button', { name: 'Collapse All' }), label: "Button 'Collapse All'" },
            { locator: this.page.getByRole('button', { name: 'Supplier toggle Supplier' }), label: "Button 'Supplier toggle Supplier'" },
            { locator: this.page.getByRole('button', { name: 'User Management toggle User' }), label: "Button 'User Management toggle User'" },
            { locator: this.page.getByRole('button', { name: 'Role Management toggle Role' }), label: "Button 'Role Management toggle Role'" },
            { locator: this.page.getByRole('button', { name: 'Facility Management toggle' }), label: "Button 'Facility Management toggle'" },
        ];

        for (const { locator, label } of elementsToVerify) {
            await this.base.verifyWithLogging(locator, label);
        }


    }
    /**
     * Verifies the presence of essential UI elements on the Edit Role page.
     *
     * This method checks for specific headings, labels, and buttons to ensure
     * the Edit Role page is rendered correctly. It validates the existence of
     * elements such as 'Assigned to', 'Edit Role', 'Modules & Permissions', and
     * various buttons and textboxes related to role management and permissions.
     * Each element is verified with logging for traceability.
     *
     * @returns {Promise<void>} Resolves when all verifications are complete.
     */
    async verifyEditRolePageButtons() {
        await this.verifyRolePage({
            headingText: 'Edit Role',
            mainTexts: ['Modules & Permissions'],
            pageSpecificElements: [
                { locator: this.page.getByRole('button', { name: 'Filter by' }), label: "Button 'Filter by'" },
                { locator: this.page.getByRole('button', { name: 'Export' }), label: "Button 'Export'" },
                { locator: this.page.getByRole('button', { name: 'Save' }), label: "Button 'Save'" },
                { locator: this.page.getByRole('button', { name: 'Reset Changes' }), label: "Button 'Reset Changes'" },
            ]
        });

    }

    /**
     * Copies a role by interacting with the role copy page.
     *
     * This method performs the following actions:
     * 1. Verifies the presence of copy role page buttons.
     * 2. Clicks the 'Reset' button and verifies the reset role popup.
     * 3. Closes the reset dialog.
     * 4. Clicks the 'Save & Add' button to copy the role.
     * 5. Verifies that a success toast message is displayed.
     * 6. Verifies the presence of role view page buttons.
     *
     * @returns {Promise<void>} A promise that resolves when the role has been copied and all verifications are complete.
     */
    async copyRole() {

        await this.verifyCopyRolePageButtons()
        await this.base.verifyResetPopup();
        await this.page.getByRole('button', { name: 'Save & Add' }).click();
        await this.base.verifyToast("Role copied successfully");
        await this.verifyRoleViewPageButtons();
    }

    /**
     * Updates a copied role by searching for the role with the name `${rolename} - Copy`,
     * editing its details with newly generated role data, assigning and unassigning users,
     * saving the changes, verifying the update via a toast message and role verification,
     * and finally deleting the updated role after searching for it.
     *
     * @param rolename - The base name of the role to be copied and updated.
     * @returns A promise that resolves when the role update and deletion process is complete.
     */
    async updatecopyRole(rolename: String) {
        await clickElement(this.elements.rolesHomeLink);
        //await this.searchbyRole(rolename+"-Copy");
        await this.searchbyRole(`${rolename} - Copy`)
        await setTimeout(1000);
        await clickElement(this.elements.roleGridEditBtn);
        const role: Roles = generateRole();
        fixture.logger.info(JSON.stringify(role));
        await this.fillRoleDetails(role);
        await this.assignUser();
        await this.unassignUser();
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Role updated successfully");
        await this.verifyRole(role);
        await setTimeout(1000);
        fixture.commonContext.roles.push({ rolename: role.rolename, roledescription: role.roledescription });
        await clickElement(this.elements.rolesHomeLink);
        await this.searchRole(role);
        await this.deleteRole();
    }



    /**
     * Applies a filter to the "Assigned To" grid based on the provided filter name and value.
     * Handles different filter types such as Brand, Name, Username, FacilityGroup, Facility, and Supplier.
     * Interacts with UI elements to select or input the filter value, triggers the search,
     * waits for the results, and verifies the grid data or displays a message if no data is found.
     * Also resets the filter after verification.
     *
     * @param filterName - The name of the filter to apply (e.g., 'Brand', 'Name', 'Username', 'FacilityGroup', 'Facility', 'Supplier').
     * @param filterValue - The value to filter by.
     * @returns A promise that resolves when the filter operation and verification are complete.
     */
    async assignedTofilterBy({ filterName, filterValue }: { filterName: string; filterValue: string; }): Promise<void> {

        const multiFilterEnabled = filterName === "MultiFilter";
        const applyFilter = async (name: string, value: string) => {

            if (await this.elements.filterBySearchButton.isHidden()) {
                await waitAndClickElement(this.elements.filterByButton, 200);
            }
            switch (name) {
                case 'Brand':
                    await clickElement(this.elements.brandDropdown);
                    await fixture.page.waitForSelector(this.brandOptSelectFlt);
                    await forceClickElement(this.getBrandOption(value));
                    await this.base.escapeOnDropdown();
                    break;
                case 'Name':
                    await fillElement(this.elements.filterByNameInput, value);
                    break;
                case 'Username':
                    await fillElement(this.elements.filterByUsernameInput, value);
                    break;
                case 'FacilityGroup':
                    await clickElement(this.elements.fgDropdown);
                    await this.base.waitAndClickLocator(this.elements.getSearchOption);
                    await fillElement(this.elements.getSearchOption, value);
                    await setTimeout(1000);
                    await forceClickElement(this.getBrandOption(value).first());
                    await this.base.escapeOnDropdown();
                    break;
                case 'Facility':
                    await clickElement(this.elements.facilityDropdown);
                    await fillElement(this.elements.getSearchOption, value);
                    await setTimeout(500);
                    await forceClickElement(this.getBrandOption(value).first());
                    await this.base.escapeOnDropdown();
                    break;
                case 'Supplier':
                    await clickElement(this.elements.supplierDropdown);
                    await forceClickElement(this.getBrandOption(value).first());
                    await this.base.escapeOnDropdown();
                    break;
                default:
                    fixture.logger.warn(`Unknown filter: ${name}`);
            }
        }

        // Handle multiple filters if MultiFilter is passed
        if (multiFilterEnabled) {
            const filters = filterValue.split(',').map(f => f.trim()); // Example: "Brand:CPS, Status:Inactive"
            for (const f of filters) {
                const [name, value] = f.split(':').map(s => s.trim());
                await applyFilter(name, value);
                fixture.logger.warn(`applying filter: ${name}, and value: ${value}`);
            }
        } else {
            await applyFilter(filterName, filterValue);
            fixture.logger.warn(`applying filter: ${filterName}, and value: ${filterValue}`);
        }

        await clickElement(this.elements.filterBySearchButton);
        // Wait for table rows to be visible
        try {
            await this.base.waitForResponse("search");
            await setTimeout(500);
            await this.base.waitForSelectorVisible(this.tableRowSelector);
            const rowCount = await this.elements.tableRows.count();
            fixture.logger.info(`Filter By : ${filterName} :Row count in grid: ${rowCount}`);
            await assert.assertToBeGreaterThanEqual(rowCount, 1);

        } catch (error) {
            // If no rows are visible, check for 'No data found' message
            if (await this.elements.gridMessage.isVisible()) {

                await expect(this.elements.gridMessage).toHaveText(/^(No Users Assigned|No data found)$/);
            }
            fixture.logger.info(`Grid display: No Users Assigned or Failed to display`);
            //await expect(this.page.getByRole('main')).toContainText('No Users Assigned');
        }
        await setTimeout(1000);
        const rowCount = await this.elements.tableRows.count();
        if (rowCount > 1) {

            await this.base.verifyPaginationText();
            if (!multiFilterEnabled) {
                await this.verifyAssignedtoGridData({ filterName, filterValue });
                //await this.verifyUserGridData({ filterName, filterValue,rowCount });
            }
        }
        if (await this.elements.resetButton.isHidden()) {
            await forceClickElement(this.elements.filterByButton);
        }
        await clickElement(this.elements.resetButton);
        await this.base.waitForResponse("search");
        await setTimeout(500);
    }

    async verifyAssignedtoGridData(
        {
            filterName,
            filterValue,
            rowCount = 1 // <-- new param (default = 1, so old calls still work)
        }: { filterName: string; filterValue: string; rowCount?: number }
    ): Promise<void> {

        // Map filterName to column index
        const columnMap: Record<string, number> = {
            'Name': 1,
            'Username': 1,
            'Facility Group': 2,
            'Facility': 3,
            'Supplier': 4
        };

        // Iterate through rows instead of hardcoding row 1
        for (let row = 1; row <= rowCount; row++) {
            if (columnMap[filterName]) {
                const cellText = await this.getTableCell(row, columnMap[filterName]).innerText();
                fixture.logger.info(
                    `Row ${row}, Col ${columnMap[filterName]}: got [${cellText}] vs Expected [${filterValue}]`
                );
                await assert.assertStringContainingIgnoreCase(cellText, filterValue);
                continue;
            }

            // For filters not handled above
            if (filterName !== 'Brand' && filterName !== '3rdPartyApp') {
                fixture.logger.warn(`Unknown filter: ${filterName}`);
            }
        }
    }

    /**
     * Verifies that a permission is enabled for a module.
     * @param permission - Permission name
     * @param module - Module name
     */
    async verifyPermission(permission: string, module: string): Promise<void> {
        const moduleToggle = this.moduleToggleMap[module]?.();
        if (!moduleToggle) {
            fixture.logger.warn(`Unknown module: ${module}`);
            return;
        }
        await clickElement(moduleToggle);
        const permissionToggle = this.getPermissionToggle(permission);
        await expect(permissionToggle).toHaveAttribute('data-state', 'checked', { timeout: 5000 });
        fixture.logger.info(`Permission "${permission}" verified successfully for module "${module}".`);
    }

    /**
     * Verifies that the role is created and displayed correctly.
     * @param role - Role details object
     */
    async verifyRole(role: Roles): Promise<void> {
        fixture.logger.info(`Verifying created role: ${role.rolename}`);
        try {
            await setTimeout(2000);

            // Verify role name and description are displayed and correct
            for (const [label, value] of [
                ["role name", role.rolename],
                ["role description", role.roledescription]
            ]) {
                fixture.logger.info(`Checking for ${label}: ${value}`);
                await expect(this.getRoleTextLocator(value)).toBeVisible({ timeout: 10000 });
                await expect(this.getRoleTextLocator(value)).toContainText(value, { timeout: 5000 });
                fixture.logger.info(`✓ ${label.charAt(0).toUpperCase() + label.slice(1)} verified: ${value}`);
            }

            fixture.logger.info(`Role verification completed successfully for: ${role.rolename}`);
        } catch (error) {
            fixture.logger.error(`Failed to verify role: ${error.message}`);
            throw new Error(`Role verification failed for: ${role.rolename}. ${error.message}`);
        }
    }

    /**
     * Creates a new role the role.
     * @param options - Permission and module for the role
     */
    async createRole(options: { permission: string; module: string; }): Promise<void> {
        await clickElement(this.elements.rolesLink);
        const role: Roles = generateRole();
        fixture.logger.info(JSON.stringify(role));
        await this.elements.createRoleButton.evaluate((node) => node.click());
        await fillElement(this.elements.roleNameInput, role.rolename);
        await this.fillRoleDetails(role);
        await this.addRolePermissions(options.permission, options.module);
        await clickElement(this.elements.saveAndAddButton);
        await this.base.verifyToast("Role added successfully");
        fixture.commonContext.roles.push({ rolename: role.rolename, roledescription: role.roledescription });
    }

    /**
     * Creates a new role, assigns permissions and users, verifies, and deletes the role.
     * @param options - Permission and module for the role
     */

    async createViewEditRole(options: { permission: string; module: string; }): Promise<void> {
        const role: Roles = generateRole();
        fixture.logger.info(JSON.stringify(role));
        await this.elements.createRoleButton.evaluate((node) => node.click());

        fixture.logger.info(`Current URL: ${this.page.url()}`);
        fixture.logger.info(`Page title: ${await this.page.title()}`);
        await this.verifyCreateRolePageButtons();
        await fillElement(this.elements.roleNameInput, role.rolename);
        await this.base.verifyunsavedChangePopup();
        await this.searchPermission(options.permission);
        await this.verifyAssignActiveuser();
        await this.fillRoleDetails(role);
        await this.addRolePermissions(options.permission, options.module);
        await clickElement(this.elements.saveAndAddButton);
        await this.base.verifyToast("Role added successfully");
        fixture.commonContext.roles.push({ rolename: role.rolename, roledescription: role.roledescription });
        await this.verifyRole(role);
        await this.verifyPermission(options.permission, options.module);
        await this.verifyRoleViewPageButtons();
        await clickElement(this.elements.editButton);
        await this.verifyEditRolePageButtons();
        await this.assignUser();
        await this.unassignUser();
        await clickElement(this.elements.saveButton);
        await this.base.verifyToast("Role updated successfully");
        await setTimeout(1000);
        await this.verifyNewRoleInUserForm(role);
        await clickElement(this.elements.rolesHomeLink);
        await this.base.waitForResponse("Role");
        await this.searchRole(role);
        await this.deleteRole();
    }


    //verify new role apear in add new user page will be covered in account manager end to end flow feature

    async verifyNewRoleInUserForm(role: Roles) {

        await clickElement(this.elements.userHomeLink);
        await this.page.locator('button[aria-label="Add New User"]').click();
        await this.base.waitForResponse("roles");
        //await assert.assertElementContainsText(this.newRoleNameOpt(role.rolename), role.rolename);
        await assert.assertElementContainsText(this.getroleNameOption(role.rolename), role.rolename);

    }



    /**
     * Verifies the column names in the roles grid and checks the presence of key UI elements on the roles page.
     *
     * This method performs the following verifications:
     * - Ensures the grid displays the expected column headers.
     * - Checks the pagination text.
     * - Validates the presence of the 'Roles' heading.
     * - Validates the presence of the 'Search' textbox.
     * - Validates the presence of the 'Create New Role' button.
     * - Validates the presence of the pagination current selection dropdown.
     * - Validates the presence of pagination arrow icons.
     *
     * @param expectedHeaders - An array of strings representing the expected column headers in the roles grid.
     * @returns A promise that resolves when all verifications are complete.
     */
    async verifyRoleGridColumnNames(expectedHeaders: string[]): Promise<void> {
        await this.base.verifyGridColumnNames(expectedHeaders);
        await this.base.verifyPaginationText();

        await this.base.verifyWithLogging(
            this.elements.rolesHeading,
            "Role Page Heading 'Roles'"
        );

        await this.base.verifyWithLogging(
            this.elements.searchRoleInput,
            "Search Input Textbox 'Search'"
        );

        await this.base.verifyWithLogging(
            this.elements.createRoleBtn,
            "Button 'Create New Role'"
        );

        await this.base.verifyWithLogging(
            this.elements.paginationCurrentBtn,
            "Pagination Current 'Select drop down'"
        );

        await this.base.verifyWithLogging(
            this.elements.paginationIconBtn,
            "Pagination 'doble or single arrow'"
        );
    }



    /**
     * Filters the role grid based on the provided filter name and value.
     *
     * Supported filter names:
     * - 'Role Name'
     * - 'Role Description'
     * - 'Active Users'
     * - 'Inactive Users'
     *
     * For supported filters, fills the search input and waits for the grid to update.
     * Validates that at least one row is present, or checks for a 'No data found' message if no rows are visible.
     * If multiple rows are found, verifies the grid data against the filter criteria.
     * Clears the search input after filtering.
     *
     * @param filter - An object containing:
     *   @param filter.filterName - The name of the filter to apply.
     *   @param filter.filterValue - The value to filter by.
     * @returns A promise that resolves when filtering and validation are complete.
     */
    async filterBy(
        { filterName, filterValue }: { filterName: string; filterValue: string }
    ): Promise<void> {

        switch (filterName) {
            case 'Role Name':
            case 'Role Description':
            case 'Active Users':
            case 'Inactive Users':
                await fillElement(this.elements.searchRoleInput, filterValue);
                break;

            default:
                fixture.logger.warn(`Unknown filter: ${filterName}`);
                return;
        }

        // Wait for grid response and validate results
        try {
            await setTimeout(500);
            await this.base.waitForSelectorVisible(this.tableRowSelector);
            const rowCount = await this.elements.tableRows.count();
            fixture.logger.info(`Filter By: ${filterName} → Row count in grid: ${rowCount}`);
            await assert.assertToBeGreaterThanEqual(rowCount, 1);

        } catch (error) {
            // If no rows are visible, check for 'No data found' message
            const gridMessageText = await this.elements.gridMessage.innerText();
            fixture.logger.info(`Grid message: ${gridMessageText}`);
            await assert.assertStringContaining(gridMessageText.trim(), "No data found");
        }

        await setTimeout(1000);
        const rowCount = await this.elements.tableRows.count();
        if (rowCount > 1) {

            await this.verifyRoleGridData({ filterName, filterValue });
            //await this.verifyRoleGridData({ filterName, filterValue,rowCount });
        }

        await this.elements.searchRoleInput.clear();

    }

    async verifyRoleGridData(
        {
            filterName,
            filterValue,
            rowCount = 1 // default = 1
        }: { filterName: string; filterValue: string; rowCount?: number }
    ): Promise<void> {

        // Map filterName to column index
        const columnMap: Record<string, number> = {
            'Role Name': 1,
            'Role Description': 2,
            'Active Users': 3,
            'Inactive Users': 4
        };

        // Iterate through rows instead of hardcoding row 1
        for (let row = 1; row <= rowCount; row++) {
            if (columnMap[filterName]) {
                const cellText = await this.getTableCell(row, columnMap[filterName]).innerText();
                fixture.logger.info(
                    `Row ${row}, Col ${columnMap[filterName]}: got [${cellText}] vs Expected [${filterValue}]`
                );
                await assert.assertStringContainingIgnoreCase(cellText, filterValue);
                continue;
            }

            // For filters not handled in columnMap (and ignoring Action, Active Users, Inactive Users)
            if (filterName !== 'Action') {
                fixture.logger.warn(`Unknown filter: ${filterName}`);
            }
        }
    }


    /**
     * Verifies that the grid column names match the expected headers and checks pagination text.
     * Simulates user interactions to assign a user by clicking relevant UI elements,
     * selects a company option, and triggers a user search.
     * After the search, re-verifies the grid column names.
     *
     * @param expectedHeaders - An array of expected grid column header names to verify.
     * @returns A promise that resolves when all verifications and interactions are complete.
     */
    async verifyAssigningToGridColumnNames(expectedHeaders: string[]): Promise<void> {
        await this.base.verifyGridColumnNamesWithSymbols(expectedHeaders, true);
        await this.base.verifyPaginationText();
        await clickElement(this.elements.assignUserButton);
        await clickElement(this.elements.brandDropdown);
        await clickElement(this.getCompanyOption(1));
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.searchUserButton);
        await setTimeout(1000);
        await this.base.verifyPaginationText(true);
        await this.base.verifyGridColumnNamesWithSymbols(expectedHeaders, true, true);
    }


    /**
     * Downloads an Excel file by clicking the export button and verifies its contents.
     *
     * The method constructs a regex pattern for the expected filename using the provided role name,
     * then calls `ExcelHelper.downloadAndVerifyExcel` to perform the download and validation.
     * The validation checks that the file name matches the pattern and that the Excel file contains
     * the expected headers at the specified row index.
     *
     * @param roleName1 - The role name used to construct the expected Excel filename pattern.
     */
    async downlaodExcelAndVerify(roleName1: String) {
        const regex = new RegExp(`^${roleName1}_Export_\\d{4}-\\d{2}-\\d{2}\\.xlsx$`);
        await ExcelHelper.downloadAndVerifyExcel(this.page, this.elements.exportButton, {
            regexPattern: regex,
            expectedHeaders: [
                'Brand',
                'Division Number',
                'Client Number',
                'Facility Name',
                'Facility Group',
                'Last Name, First Name',
                'Username',
                'Status',
                'Account Manager',
                'Regional Director',
                'Vice President Operations',
                'Client Relationship Owner',
                'Supplier ID',
                'Supplier Name'
            ],
            headerRowIndex: 3
        });


    }

}