import { expect, Page } from "@playwright/test";
import PlaywrightWrapper from "../helper/wrapper/PlaywrightWrappers";
import { User } from "../helper/types/user";
import { generateUser } from "../helper/util/common";
import { ExcelHelper } from '../helper/util/ExcelHelper';
import * as path from 'path';
import * as fs from 'fs';
import { clickElement, fillElement, forceClickElement } from "../helper/wrapper/actions";
import Assert from "../helper/wrapper/assert";
import { setTimeout } from 'timers/promises';
import { fixture } from "../hooks/pageFixture";

let assert: Assert;

/**
 * Page Object Model for User Home Page.
 * Provides methods to interact with and verify UI elements, perform search and filter actions,
 * validate grid data, and download/export user data.
 */
export default class UserHomePage {

    private base: PlaywrightWrapper;

    constructor(private page: Page) {
        this.base = new PlaywrightWrapper(page);
        assert = new Assert(page);
    }

    // Locators for User Home Page elements
    private elements = {
        searchButton: this.page.getByRole('button', { name: 'Search' }),
        collapseClosedButton: this.page.locator('main div[data-state="closed"] button[data-slot="collapsible-trigger"]'),
        collapseOpenButton: this.page.locator('main div[data-state="open"] button[data-slot="collapsible-trigger"]'),
        gridMessage: this.page.locator('.flex>img+p'),
        usernameInput: this.page.getByRole('textbox', { name: 'Username' }),
        nameInput: this.page.getByRole('textbox', { name: 'Name', exact: true }),
        emailInput: this.page.getByRole('textbox', { name: 'Email' }),
        brandDropdown: this.page.getByRole('combobox', { name: 'Brand' }),
        cpsBrandCheckbox: this.page.getByRole('checkbox', { name: 'Select CPS' }),
        facilityGroupDropdown: this.page.locator('.grid>.flex:nth-child(5) button'),
        facilityDropdown: this.page.locator('.grid>.flex:nth-child(6) button'),
        thirdPartyDropdown: this.page.getByRole('combobox', { name: '3rd Party Application Access' }),
        activeCheckbox: this.page.getByRole('checkbox', { name: 'Active', exact: true }),
        resetButton: this.page.getByRole('button', { name: 'Reset' }),
        tableRows: this.page.locator('div[data-slot="table-container"] tbody>tr'),
        exportButton: this.page.getByRole('button', { name: 'Export users' }),
        searchOptionInput: this.page.locator('input[data-slot="command-input"]')
    };

    private tableRowSelector = 'div[data-slot="table-container"] tbody>tr';

    private getBrandOption = (brand: string) =>
        this.page.locator(`div[data-slot="command-group"] div[data-slot="command-item"]:has-text("${brand}")`);

    private getColumnHeader = (index: number) =>
        this.page.locator('div[data-slot="table-container"] thead>tr th').nth(index);

    private getTableCell = (rowIndex: number, colIndex: number) =>
        this.page.locator(`div[data-slot="table-container"] tbody>tr:nth-child(${rowIndex})>td`).nth(colIndex);

    private getStatusCheckbox = (index: number) =>
        this.page.locator('.grid>div:nth-child(8) button').nth(index);

    private getCipContactCheckbox = (index: number) =>
        this.page.locator('.grid>div:nth-child(9) button').nth(index);

    private getMultiUnitCheckbox = (index: number) =>
        this.page.locator('.grid>div:nth-child(10) button').nth(index);

    /**
     * Searches for users with default filters (CPS brand and active status).
     */
    async searchUser(): Promise<void> {
        await clickElement(this.elements.brandDropdown);
        await clickElement(this.elements.cpsBrandCheckbox);
        await this.base.escapeOnDropdown();
        await clickElement(this.elements.activeCheckbox);
        await clickElement(this.elements.searchButton);
        await this.page.waitForLoadState("domcontentloaded");
    }

    /**
     * Searches for a specific user by username.
     * @param username - The username to search for.
     */
    async searchSpecificUser(username: string): Promise<void> {
        await fillElement(this.elements.usernameInput, username);
        await clickElement(this.elements.searchButton);
        await setTimeout(500);
    }

    /**
     * Applies filters to the user grid based on filter name and value.
     * Supports single and multi-filter scenarios.
     * @param filter - Object containing filterName and filterValue
     */
    async filterBy(
        { filterName, filterValue }: { filterName: string; filterValue: string; }
    ): Promise<void> {
        const multiFilterEnabled = filterName === "MultiFilter";
        const applyFilter = async (name: string, value: string) => {
            switch (name) {
                case 'Brand':
                    await clickElement(this.elements.brandDropdown);
                    await forceClickElement(this.getBrandOption(value));
                    await this.base.escapeOnDropdown();
                    break;
                case 'Name':
                    await fillElement(this.elements.nameInput, value);
                    break;
                case 'Username':
                    await fillElement(this.elements.usernameInput, value);
                    break;
                case 'Email':
                    await fillElement(this.elements.emailInput, value);
                    break;
                case 'FacilityGroup':
                    await clickElement(this.elements.facilityGroupDropdown);
                    await this.base.waitAndClickLocator(this.elements.searchOptionInput);
                    await fillElement(this.elements.searchOptionInput, value);
                    await setTimeout(1000);
                    await forceClickElement(this.getBrandOption(value).first());
                    await this.base.escapeOnDropdown();
                    break;
                case 'Facility':
                    await clickElement(this.elements.facilityDropdown);
                    await fillElement(this.elements.searchOptionInput, value);
                    await setTimeout(500);
                    await forceClickElement(this.getBrandOption(value).first());
                    await this.base.escapeOnDropdown();
                    break;
                case '3rdPartyApp':
                    await clickElement(this.elements.thirdPartyDropdown);
                    await forceClickElement(this.getBrandOption(value));
                    await this.base.escapeOnDropdown();
                    break;
                case 'Status':
                    await forceClickElement(
                        value === 'Active' ? this.getStatusCheckbox(0) : this.getStatusCheckbox(1)
                    );
                    break;
                case 'CIPContact':
                    await forceClickElement(
                        value === 'Yes' ? this.getCipContactCheckbox(0) : this.getCipContactCheckbox(1)
                    );
                    break;
                case 'MultiUnit':
                    await forceClickElement(
                        value === 'Yes' ? this.getMultiUnitCheckbox(0) : this.getMultiUnitCheckbox(1)
                    );
                    break;
                default:
                    fixture.logger.warn(`Unknown filter: ${name}`);
            }
        };

        // Handle multiple filters if MultiFilter is passed
        if (multiFilterEnabled) {
            const filters = filterValue.split(',').map(f => f.trim()); // Example: "Brand:CPS, Status:Inactive"
            for (const f of filters) {
                const [name, value] = f.split(':').map(s => s.trim());
                await applyFilter(name, value);
                fixture.logger.warn(`applying filter: ${name}, and value: ${value}`);
            }
        } else {
            await applyFilter(filterName, filterValue);
            fixture.logger.warn(`applying filter: ${filterName}, and value: ${filterValue}`);
        }

        await clickElement(this.elements.searchButton);
        try {
            await this.base.waitForResponse("filter");
            await setTimeout(500);
            await this.base.waitForSelectorVisible(this.tableRowSelector);
            const rowCount = await this.elements.tableRows.count();
            fixture.logger.info(`Filter By : ${filterName} :Row count in grid: ${rowCount}`);
            await assert.assertToBeGreaterThanEqual(rowCount, 1);
        } catch (error) {
            const gridMessageText = await this.elements.gridMessage.innerText();
            fixture.logger.info(`Grid message: ${gridMessageText}`);
            await assert.assertStringContaining(gridMessageText.trim(), "No data found");
        }
        await setTimeout(1000);
        const rowCount = await this.elements.tableRows.count();
        if (rowCount > 1) {
            await this.base.verifyPaginationText();
            if (!multiFilterEnabled) {
                await this.verifyUserGridData({ filterName, filterValue });
            }
        }
        if (await this.elements.collapseOpenButton.isHidden()) {
            await forceClickElement(this.elements.collapseClosedButton);
        }
        await clickElement(this.elements.resetButton);
    }

    /**
     * Verifies grid data and pagination for the applied filter.
     * Checks cell values and attributes based on filter type.
     * @param params - Object containing filterName, filterValue, and optional rowCount
     */
    async verifyUserGridData(
        {
            filterName,
            filterValue,
            rowCount = 1
        }: { filterName: string; filterValue: string; rowCount?: number }
    ): Promise<void> {

        const columnMap: Record<string, number> = {
            'Name': 1,
            'Username': 1,
            'Email': 1,
            'Facility Group': 2,
            'Facility': 3
        };

        for (let row = 1; row <= rowCount; row++) {
            if (columnMap[filterName]) {
                const cellText = await this.getTableCell(row, columnMap[filterName]).innerText();
                fixture.logger.info(
                    `Row ${row}, Col ${columnMap[filterName]}: got [${cellText}] vs Expected [${filterValue}]`
                );
                await assert.assertStringContainingIgnoreCase(cellText, filterValue);
                continue;
            }

            if (filterName === 'Status') {
                const expectedAttr = filterValue === 'Active' ? 'true' : 'false';
                await assert.assertElementHasAttributeValue(
                    this.getTableCell(row, 6).locator('button'),
                    'aria-checked',
                    expectedAttr
                );
                continue;
            }

            if (filterName === 'CIPContact') {
                const expectedAttr = filterValue === 'Yes' ? 'Check' : 'Close';
                await assert.assertElementHasAttributeValue(
                    this.getTableCell(row, 4).locator('svg'),
                    'aria-label',
                    expectedAttr
                );
                continue;
            }

            if (filterName === 'MultiUnit') {
                const expectedAttr = filterValue === 'Yes' ? 'Check' : 'Close';
                await assert.assertElementHasAttributeValue(
                    this.getTableCell(row, 5).locator('svg'),
                    'aria-label',
                    expectedAttr
                );
                continue;
            }

            if (filterName !== 'Brand' && filterName !== '3rdPartyApp') {
                fixture.logger.warn(`Unknown filter: ${filterName}`);
            }
        }
    }

    /**
     * Verifies that the user grid contains the expected column headers.
     * @param expectedHeaders - Array of expected header strings
     */
    async verifyUserGridColumnNames(expectedHeaders: string[]): Promise<void> {
        await this.filterBy({ filterName: "Brand", filterValue: "CPS" });
        await this.base.verifyGridColumnNamesWithSymbols(expectedHeaders);
        //await clickElement(this.elements.resetButton);
    }

    /**
     * Downloads the Excel file and verifies its data.
     * Checks the file name pattern and validates the header row.
     */
    async downloadExcelAndVerify(): Promise<void> {
        await ExcelHelper.downloadAndVerifyExcel(this.page, this.elements.exportButton, {
            regexPattern: /^UserExport_\d{2}_[A-Z]{3}_\d{4}\.xlsx$/,
            expectedHeaders: [
                'Last Name',
                'First Name',
                'Username',
                'Email',
                'Brand Name',
                'Facility Group',
                'Facility Name',
                'Division Number',
                'Client ID',
                'CIP Contact',
                'Multi-Unit',
                'Status',
                '3rd Party Application Name',
                '3rd Party Application Username',
                'Account Manager',
                'Vice President of Operations'
            ],
            headerRowIndex: 7
        });
    }

}