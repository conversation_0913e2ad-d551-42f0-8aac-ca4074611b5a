@auth
Feature: Manage Facilities Screen - "Filter By" Search Area - Field Validation

    @FacilityGroupGrid
    Scenario: Super Admin - Verify Facility Group Grid Validations
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the facilites page
        And User verify the Facility group grid with following columns names
            """
            Facility Group
            Brand
            No. of Facilities
            Action
            """
        And User search for a Facility Group filter by
            | FilterName    | Filter value               |
            | Brand         | GESPRA                     |
            | FacilityGroup | Demo                       |
            | Status        | Active                     |
            | MultiFilter   | Brand:CPS, Status:Inactive |

    @FacilityGrid
    Scenario: Super Admin - Verify Facilites Grid Validations
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the facilites page
        And User verify the Facilites grid with following columns names
            """
            Facility
            Facility Group
            Division Number
            Client Number
            Account Manager
            Action
            """
        And User search for a facility filter by
            | FilterName     | Filter value                                |
            | Brand          | GESPRA                                      |
            | Facility       | Test                                        |
            | MultiFilter    | Facility:Test,Brand:GESPRA, Status:Inactive |
            | MultiFilter    | Facility:ATest,Brand:QUASEP, Status:Active  |
            | DivisionNumber | 123                                         |
            | ClientNumber   | 123                                         |
            | Status         | Active                                      |
            | MultiFilter    | Facility:Test12,Brand:CPS, Status:Active    |