@auth
Feature:  Facility Group | Creation/View/Edit Screen

    @CreateEditViewandSearchFacilityGroup @smoke
    Scenario:Super Admin - eCPS - Facilities - Internal Facility Group - Add/View/Edit Functionality
        Given User navigates to the application Login Page
        And User enters the username
        And User enters the password
        When User clicks on the SignIn button
        Then User navigates to the Home page
        Then User navigates to the facilites page
        When User created a FacilityGroup and verify edit view and search functionality
            | Company    |
            | CPS        |
            | GESPRA     |
            | QUASEP     |
            | Instructor |