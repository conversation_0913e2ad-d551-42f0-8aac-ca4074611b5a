@auth
Feature: Role Management-Verify Role Create/View/Edit Functionality

    @createRole
    Scenario: Super Admin - Create new Role Functionality and verify Role info page and Edit role details
        Given User navigates to the application Login Page
        And User enters the username
        And User enters the password
        When User clicks on the SignIn button
        Then User navigates to the Home page
        Then User navigates to the role page
        When User created a new role and User should be able to verify role info and modify role details
            | Permission                                    | Module              |
            | Create Supplier User                          | Supplier            |
            | Create/Edit Internal Facility/Facility Groups | Facility Management |
            | Create / Copy / Edit                          | Role Management     |
            | Create User                                   | User Management     |
            | All Permission                                | Facility Management |


    @rolecopy
    Scenario: Super Admin - Verify Copy role Functionality
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the role page
        And the user searches for a Role "Bell Gardner role" using the filter
        And the user clicks on the "Copy" button in the filtered table grid
        And verify permission set an update if needed and save the role
        Then the newly copied role details should be displayed and verified














