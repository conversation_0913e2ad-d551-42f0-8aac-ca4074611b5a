@auth
Feature: Edit  External User Functionality

    @edituserext
    Scenario: Super Admin -  External User Page Validation
        Given User navigates to the application with valid super admin credentials
        When the user navigates to the user page
        And User search for a External user "christophe"
        And User verify External User page with following fields inactive
        When User edit an existing external user
            | Role              |
            | Regional Director |