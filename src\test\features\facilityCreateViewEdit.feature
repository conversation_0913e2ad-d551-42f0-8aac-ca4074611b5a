@auth
Feature: Facility Create View Edit Functionality

    @CreateEditViewandSearchFacility @smoke
    Scenario:Super Admin - Create Edit and view Facility Functionality
        Given User navigates to the application Login Page
        And User enters the username
        And User enters the password
        When User clicks on the SignIn button
        Then User navigates to the Home page
        Then User navigates to the facilites page
        When User created a Facility and verify edit view and search functionality
            | Company    | AddressType |
            | GESPRA     | Other       |
            | QUASEP     | Shipping    |
            | CPS        | Main        |
            | Instructor | Billing     |



