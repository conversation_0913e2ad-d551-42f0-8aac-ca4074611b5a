@auth
Feature: User Registration and Verification
    @test
    Scenario Outline:  Super Admin - Register new User and verify View page and Edit page details
        Given User navigates to the application Login Page
        And User enters the username
        And User enters the password
        When User clicks on the SignIn button
        Then User navigates to the Home page
        And User clicks on add new user button
        Then User should redirect to Add New User form  and then enter Valid data to save and Register "<Company>", "<Role>", "<PhoneType>", "<AddressType>", "<3rd Party>", "<Functional Area>"
        Then User should be view/edit a newly created user
        Examples:
            | Company | Role                  | PhoneType   | AddressType | 3rd Party  | Functional Area                                                                        |
            | GESPRA  | Education Coordinator | Fax         | Other       | Dashboard  | Administration, Activation, Nursing, Environmental, Laundry, Housekeeping, Foodservice |
            | QUASEP  | CIP Financial Analyst | Main        | Billing     | PartsTown  | not present                                                                            |
            | CPS     | Regional Director     | Cell/Mobile | Main        | Purchasing | Administration, Office Services                                                        |
            | GESPRA  | Account Manager       | Cell/Mobile | Main        | Purchasing | Activation, Nursing, Environmental, Maintenance                                        |
            | GESPRA  | Client Group Admin    | Work        | Shipping    | HUBERT     | Administration, Activation, Nursing, Environmental, Maintenance, Office Services       |






