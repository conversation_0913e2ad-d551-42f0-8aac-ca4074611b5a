@auth
Feature: Role View Page Assigned to Section Filter By search and Export Functionality

    @roleAsignedFilterBy
    Scenario: Super Admin - Verify Role Assigned to section Export and Filter By Search Functionality
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the role page
        And the user searches for a Role "External Supplier" using the filter
        And the user clicks on the "View" button in the filtered table grid
        And click on export Button and verify its content
        And Verify Filter By Search with fallowing items
            | FilterName    | Filter value                          |
            | Name          | Test                                  |
            | Username      | Testing                               |
            | MultiFilter   | Facility:Test,Brand:QUASEP, Name:Test |
            | Brand         | GESPRA                                |
            | FacilityGroup | Demo                                  |
            | Supplier      | dist                                  |
            | Facility      | test                                  |
