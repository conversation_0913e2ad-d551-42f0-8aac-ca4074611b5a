@auth
Feature: Role Home Page Grid and Assigned to Grid Validations

    @roleGrid
    Scenario: Super Admin - Verify Role grid Validations
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the role page
        And User verify the role grid with following columns names
            """
            Role Name
            Role Description
            Active Users
            Inactive Users
            Action
            """
        And User search for a Role filter by
            | FilterName       | Filter value            |
            | Role Name        | Create Role             |
            | Role Description | Edit Supplier User Only |



    @roleAssignedtoGrid
    Scenario: Super Admin - Verify Assigned To and Assign Active Users grid validations
        Given User navigates to the application with valid super admin credentials
        Then User navigates to the role page
        And the user searches for a Role "Admin Level" using the filter
        And the user clicks on the "View" button in the filtered table grid
        Then the Assigned To table grid should be displayed with the following column names
            """
            Name & Username
            Facility Group
            Facility Div # | Client #
            Supplier Name & ID
            """





