
@auth
Feature: User Management Filter By and Export Functionality


    @usergrid
    Scenario: Super Admin - Filter By  Functionality
        Given User navigates to the application with valid super admin credentials
        When the user navigates to the user page
        And User verify the user grid with following columns names
            """
            Last Name, First Name
            Username & Email
            Facility Group
            Facility Div # | Client #
            CIP Contact
            Multi Unit
            Status
            Action
            """
        And User search for a user filter by
            | FilterName    | Filter value               |
            | FacilityGroup | Demo                       |
            | Brand         | GESPRA                     |
            | Name          | Test                       |
            | Username      | Testing                    |
            | Email         | test                       |
            | Facility      | test                       |
            | 3rdPartyApp   | Purchasing                 |
            | Status        | Active                     |
            | CIPContact    | Yes                        |
            | MultiUnit     | No                         |
            | MultiFilter   | Brand:CPS, Status:Inactive |




    @usergridExport
    Scenario: Super Admin - User Grid Export Functionality
        Given User navigates to the application with valid super admin credentials
        When the user navigates to the user page
        And User search for a user filter by
            | FilterName  | Filter value                                               |
            | MultiFilter | Brand:CPS, Status:Active, MultiUnit:No, FacilityGroup:demo |
        And click on export Button on User Grid and verify its content
