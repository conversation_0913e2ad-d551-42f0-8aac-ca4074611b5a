import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import AccessibilityPage from "../../pages/accessibilityPage";
let accessibilityPage: AccessibilityPage;
let assert: Assert;

Then('run Accessibility Tests', async function () {
    accessibilityPage = new AccessibilityPage(fixture.page);
    assert = new Assert(fixture.page);
    await accessibilityPage.runAccessibilityReport();
})