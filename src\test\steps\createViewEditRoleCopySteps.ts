import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import RolePage from "../../pages/rolePage";
let rolePage: RolePage;
let assert: Assert;
let rolename1: String;


Then(`User navigates to the role page`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    rolePage = new RolePage(fixture.page);
    assert = new Assert(fixture.page);
    await rolePage.navigateToRolePage();
});

When(`User created a new role and User should be able to verify role info and modify role details`, async function (datatable) {

    var rs = datatable.rows();
    for (const element of rs) {
        //console.log(element[0]);
        var resp = await rolePage.createViewEditRole({
            permission: element[0],
            module: element[1]
        });
    }
});

When(`creates a new Role with {string} permission with in the module {string}.`, async function (Permission: string, Module: string) {
    // [When] Describes the action or event that triggers the scenario.
    rolePage = new RolePage(fixture.page);
    await rolePage.createRole({
        permission: Permission,
        module: Module
    });    

    //hardcoding for now to avoid too much data
    
});


Then(`User verify the role grid with following columns names`, async function (docString: string) {
    const expectedHeaders = docString
        .split('\n')
        .map(h => h.trim())
        .filter(h => h.length > 0); // remove empty lines if any
    console.log("Expected headers from feature:", expectedHeaders);
    await rolePage.verifyRoleGridColumnNames(expectedHeaders);

});

Then(`User search for a Role filter by`, async function (table3) {
    var fltrs = table3.rows();
    for (const element of fltrs) {
        //console.log(element[0]);
        var resp = await rolePage.filterBy({
            filterName: element[0],
            filterValue: element[1]
        });
    }

});

Then(`the user clicks on the {string} button in the filtered table grid`, async function (tablebtn: string) {
    // [Then] Describes the expected outcome or result of the scenario.
    await rolePage.roleButtonFromTable(tablebtn)
});

Then(`the user searches for a Role {string} using the filter`, async function (RoleName: string) {
    // [Then] Describes the expected outcome or result of the scenario.
    rolePage = new RolePage(fixture.page);
    rolename1 = RoleName;
    await rolePage.searchbyRole(rolename1);
});


Then(`the Assigned To table grid should be displayed with the following column names`, async function (gridnames: string) {
    const expectedHeaders = gridnames
        .split('\n')
        .map(h => h.trim())
        .filter(h => h.length > 0); // remove empty lines if any
    console.log("Expected headers from feature:", expectedHeaders);
    await rolePage.verifyAssigningToGridColumnNames(expectedHeaders);
});

Then(`verify permission set an update if needed and save the role`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    await rolePage.copyRole();
});

Then(`the newly copied role details should be displayed and verified`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    await rolePage.updatecopyRole(rolename1);

});

Then(`Verify Filter By Search with fallowing items`, async function (table1) {
    // [Then] Describes the expected outcome or result of the scenario.
    var fltrs = table1.rows();
    for (const element of fltrs) {
        //console.log(element[0]);
        var resp = await rolePage.assignedTofilterBy({
            filterName: element[0],
            filterValue: element[1]
        });
    }
});


Then(`click on export Button and verify its content`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    await rolePage.downlaodExcelAndVerify(rolename1);

});

