import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import FacilitesPage from "../../pages/facilitesPage";
let facilitesPage: FacilitesPage;
let assert: Assert;


Then('User navigates to the facilites page', async function () {
    facilitesPage = new FacilitesPage(fixture.page);
    assert = new Assert(fixture.page);
    await facilitesPage.navigateToFacilitiesPage();
});

When('User created a new FacilityGroup', async function (table) {
    var rs = table.rows();
    for (const element of rs) {
        var resp = await facilitesPage.createFacilityGroup({
            company: element[0],
        });
    }
});

When('User created a new Facility', async function (table1) {
    var rs = table1.rows();
    for (const element of rs) {
        var resp = await facilitesPage.createFacility({
            company: element[0],
            addressType: element[1],
        });
    }
});

When('Create New Facility and capture the details', async function () {
    // Write code here that turns the phrase above into concrete actions
    facilitesPage = new FacilitesPage(fixture.page);
    var userDetails = fixture.commonContext.getLatestUser();
    var fullName = userDetails.firstName + " " + userDetails.lastName;
    console.log(fullName);
    await facilitesPage.navigateToFacilitiesPage();
    await facilitesPage.createFacility({ company: "CPS", addressType: "Shipping", accountManager: fullName });
    //await facilitesPage.createFacility({ company: "CPS", addressType: "Shipping", accountManager: "Myrna Stehr" });
});

Then(`User verify the Facility group grid with following columns names`, async function (docString: string) {
    const expectedHeaders = docString
        .split('\n')
        .map(h => h.trim())
        .filter(h => h.length > 0); // remove empty lines if any
    console.log("Expected headers from feature:", expectedHeaders);
    await facilitesPage.verifyFacilityGroupGridColumnNames(expectedHeaders);

});

Then(`User verify the Facilites grid with following columns names`, async function (docString: string) {
    const expectedHeaders = docString
        .split('\n')
        .map(h => h.trim())
        .filter(h => h.length > 0); // remove empty lines if any
    console.log("Expected headers from feature:", expectedHeaders);
    await facilitesPage.verifyFacilitiesGridColumnNames(expectedHeaders);

});
Then(`User search for a facility filter by`, async function (filtertable) {
    // [Then] Describes the expected outcome or result of the scenario.
    var fltrs = filtertable.rows();
    for (const element of fltrs) {
        //console.log(element[0]);
        var resp = await facilitesPage.applyFacilityAndGroupFilter({
            filterName: element[0],
            filterValue: element[1]
        });
    }



});
Then(`User search for a Facility Group filter by`, async function (filtertable) {
    // [Then] Describes the expected outcome or result of the scenario.
    var fltrs = filtertable.rows();
    for (const element of fltrs) {
        //console.log(element[0]);
        var resp = await facilitesPage.applyFacilityAndGroupFilter({
            filterName: element[0],
            filterValue: element[1]

        });
    }

});

When(`creates a new Facility and user makes them inactive and verifies the popup`, async function () {
    // [When] Describes the action or event that triggers the scenario.
    await facilitesPage.createFacilityAndSetInactive();
});



Then(`User search for a External {string} with name {string}`, async function (facilityorFg: string, facilityname: string) {
    // [Then] Describes the expected outcome or result of the scenario.
    await facilitesPage.applyFacilityAndGroupFilter({
        filterName: facilityorFg,
        filterValue: facilityname

    });
});

Then(`User search for a newly creted facility`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    await facilitesPage.searchForNewFacility()
});

Then(`User verify External Facility page with following fileds inactive`, async function () {
    // [Then] Describes the expected outcome or result of the scenario.
    await facilitesPage.verifyEditFacilityDisabledFields()
});

When(`User edit an existing external Facility`, async function () {
    // [When] Describes the action or event that triggers the scenario.
    await facilitesPage.editExternalFacility();
});

When('User created a FacilityGroup and verify edit view and search functionality', async function (fgtable) {
    var rs = fgtable.rows();
    for (const element of rs) {
        var resp = await facilitesPage.createEditViewAndSearchFacilityGroup({
            company: element[0],
        });
    }
});
When('User created a Facility and verify edit view and search functionality', async function (factable) {
    var rs = factable.rows();
    for (const element of rs) {
        var resp = await facilitesPage.createEditViewAndSearchFacility({
            company: element[0],
            addressType: element[1]
        });
    }
});