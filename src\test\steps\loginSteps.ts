import { Given, When, Then, setDefaultTimeout } from "@cucumber/cucumber";

import { expect } from "@playwright/test";
import { fixture } from "../../hooks/pageFixture";
import * as data from "../../helper/util/test-data/test_data.json";
import { clickElement, waitAndClickElement, fillElement } from "../../helper/wrapper/actions";
import { setTimeout } from 'timers/promises';

setDefaultTimeout(60 * 1000 * 2)


Given(`User navigates to the application Login Page`, async function () {
    // [Given] Sets up the initial state of the system.
    await fixture.page.goto(process.env.TESTURL, {
        waitUntil: "domcontentloaded"
    });
    await setTimeout(5000);
    fixture.logger.info("Navigated to the application")
});


Given('User click on the login link', async function () {
    await setTimeout(1000);
    if (await fixture.page.locator(".login-text").isVisible()) {
        fixture.logger.info("login link click - .login-text")
        await clickElement(fixture.page.locator(".login-text"));
    }
});


Given(`User enters the username`, async function () {
    if (await fixture.page.locator("#username").isVisible()) {
        fixture.logger.info("enter username - #username");
        await fillElement(fixture.page.locator("#username"), data.ecps.userName);
    }
});


Given(`User enters the password`, async function () {
    if (await fixture.page.locator("#password").isVisible()) {
        fixture.logger.info("enter password - #password");
        await fillElement(fixture.page.locator("#password"), data.ecps.password);
        await setTimeout(1000);
        await clickElement(fixture.page.locator("#btn-login"));
    }
});



When('User click on the login button', async function () {
    if (await fixture.page.locator("#btn-login").isVisible()) {
        fixture.logger.info("signin btn - #btn-login");
        //await waitAndClickElement(fixture.page.locator("#btn-login"),5000);
        //await clickElement(fixture.page.locator("#btn-login"));
    }
    await fixture.page.waitForLoadState("domcontentloaded");
    const user = fixture.page.locator(".bg-grey");
    await expect(user).toBeVisible({ timeout: 60000 });
    await fixture.page.context().storageState({ path: "src/helper/auth/admin.json" });
});


When(`User clicks on the SignIn button`, async function () {
    if (await fixture.page.locator("#btn-login").isVisible()) {
        //fixture.logger.info("signin btn - #btn-login");
        //await waitAndClickElement(fixture.page.locator("#btn-login"),1000);
        //await clickElement(fixture.page.locator("#btn-login"));
        await fixture.page.waitForLoadState("domcontentloaded");
        const user = fixture.page.locator(".bg-grey");
        await expect(user).toBeVisible({ timeout: 30000 });
        await fixture.page.context().storageState({ path: "src/helper/auth/admin.json" });
    }
});


Then('Login should be success', async function () {
    const user = fixture.page.locator(".bg-grey");
    const userName = await user.textContent();
    fixture.logger.info("Username: " + userName);
})




