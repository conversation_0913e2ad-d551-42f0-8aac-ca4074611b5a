import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import RegisterPage from "../../pages/registerPage";
import UserHomePage from "../../pages/userHomePage";
let registerPage: RegisterPage;
let userHomePage: UserHomePage;
let assert: Assert;


Then(`User navigates to the Home page`, async function () {
    registerPage = new RegisterPage(fixture.page);
    assert = new Assert(fixture.page);
    if (await fixture.page.locator("header.flex").isVisible()) {
        fixture.logger.info("navigates To User Home Page")
    }
    if (await fixture.page.locator("div.login-container").isVisible()) {
        fixture.logger.info("navigating To Default Page")
        await registerPage.navigateToDefaultPage();
    }
});

Then(`User clicks on add new user button`, async function () {
    await registerPage.navigateToAddNewUserForm();
});

When('User should redirect to Add New User form  and then enter Valid data to save and Register {string}, {string}, {string}, {string}, {string}, {string}', async function (company, role, phoneType, addressType, thirdParty, functionalArea) {

    //console.log(element[0]);
    var resp = await registerPage.registerUser({
        company,
        role,
        phoneType,
        addressType,
        thirdParty: thirdParty,
        functionalArea,
        verifyFromEdit: true
    });

});

Then(`User should be view\\/edit a newly created user`, async function () {
    await registerPage.viewUser({ verifyFromView: true });
    await registerPage.editNewUser();
});

 When('creates a new user', async function () {
           // Write code here that turns the phrase above into concrete actions
           registerPage = new RegisterPage(fixture.page);
           await registerPage.createNewUser();
         });


When(`creates a new user with new role Created above and searches for the newly created user`, async function () {
    registerPage = new RegisterPage(fixture.page);
        //console.log(fixture.commonContext.getLatestRole());
    await registerPage.createNewUserAndSearch(fixture.commonContext.getLatestRole());
    //await registerPage.createNewUserAndSearch({ rolename: 'National Implementation Agent', roledescription: 'Internal' });
});

Then(`user makes inactive and verifies the popup`, async function () {
    await registerPage.inactivateUser();

});

Then(`User search for a registered user`, async function () {
    userHomePage = new UserHomePage(fixture.page);
    await userHomePage.searchUser();
});

When(`User search for a External user {string}`, async function (extuser: string) {
    // [When] Describes the action or event that triggers the scenario.
    userHomePage = new UserHomePage(fixture.page);
    await userHomePage.searchSpecificUser(extuser);
});

When(`User edit an existing registered user`, async function (table1) {
    registerPage = new RegisterPage(fixture.page);
    var ers = table1.rows();
    for (const element of ers) {
        //console.log(element[0]);
        var resp = await registerPage.editUser({
            company: element[0],
            role: element[1],
            phoneType: element[2],
            addressType: element[3],
            thirdParty: element[4],
            functionalArea: element[5]
        });
    }
});


When(`User edit an existing external user`, async function (table2) {
    registerPage = new RegisterPage(fixture.page);
    var ers = table2.rows();
    for (const element of ers) {
        //console.log(element[0]);
        var resp = await registerPage.editExtUser({
            role: element[0]
        });
    }
});


Then(`User verify External User page with following fields inactive`, async function () {
    registerPage = new RegisterPage(fixture.page);
    await registerPage.viewExternalUser();

});


When('newly created Role asigned to newly created user', async function () {
    // Write code here that turns the phrase above into concrete actions
    //{  rolename: 'National Implementation Agent',  roledescription: 'Internal'}    
    //console.log(fixture.commonContext.getLatestRole());
    //await registerPage.EditUserNewRole(fixture.commonContext.getLatestRole());
    await registerPage.EditUserNewRole({ rolename: 'National Implementation Agent', roledescription: 'Internal' });
});

When('Logout from the portal', async function () {
    // Write code here that turns the phrase above into concrete actions
    await registerPage.logOutFromPortal();
});



When('login to the portal with new User', async function () {
    // Write code here that turns the phrase above into concrete actions
    //{ username: 'Ilene51', password: 'P@ss*or%@123' }
    await registerPage.specificUserLogin(fixture.commonContext.getLatestUser());
    //await registerPage.specificUserLogin({ username: 'Ilene51', password: 'P@ss*or%@123' });
});






