import { Given, When, Then } from "@cucumber/cucumber";
import { fixture } from "../../hooks/pageFixture";
import Assert from "../../helper/wrapper/assert";
import RegisterPage from "../../pages/registerPage";
import UserHomePage from "../../pages/userHomePage";
let registerPage: RegisterPage;
let userHomePage: UserHomePage;
let assert: Assert;



Then(`User search for a user filter by`, async function (table3) {
    userHomePage = new UserHomePage(fixture.page);
    var fltrs = table3.rows();
    for (const element of fltrs) {
        //console.log(element[0]);
        var resp = await userHomePage.filterBy({
            filterName: element[0],
            filterValue: element[1]
        });
    }

});

Then(`User verify the user grid with following columns names`, async function (docString: string) {
    // <DataTable> argument is detected:
    userHomePage = new UserHomePage(fixture.page);
    const expectedHeaders = docString
        .split('\n')
        .map(h => h.trim())
        .filter(h => h.length > 0); // remove empty lines if any
    console.log("Expected headers from feature:", expectedHeaders);
    await userHomePage.verifyUserGridColumnNames(expectedHeaders);

});


Then(`User verify View User page with following lable names`, async function () {
    registerPage = new RegisterPage(fixture.page);
    await registerPage.viewUser();
});


When(`click on export Button on User Grid and verify its content`, async function ()  {
    // [When] Describes the action or event that triggers the scenario.
    await userHomePage.downloadExcelAndVerify()
});